"""
日志配置模块
提供统一的日志配置和格式化功能
"""

import logging
import sys
import os
from datetime import datetime
from typing import Optional
from pathlib import Path
from logging.handlers import RotatingFileHandler

# 避免循环导入，直接读取环境变量
DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')


class MillisecondFormatter(logging.Formatter):
    """支持毫秒级时间戳的日志格式化器"""
    
    def formatTime(self, record, datefmt=None):
        """格式化时间，包含毫秒"""
        ct = self.converter(record.created)
        if datefmt:
            s = datetime.fromtimestamp(record.created).strftime(datefmt)
        else:
            t = datetime.fromtimestamp(record.created)
            s = t.strftime('%H:%M:%S.%f')[:-3]  # 保留3位毫秒
        return s


def setup_logger(
    name: str,
    level: Optional[str] = None,
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    设置并返回配置好的日志器

    Args:
        name: 日志器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径（可选）
        max_file_size: 日志文件最大大小（字节）
        backup_count: 备份文件数量

    Returns:
        配置好的日志器实例
    """
    logger = logging.getLogger(name)

    # 避免重复配置
    if logger.handlers:
        return logger

    # 设置日志级别
    if level is None:
        level = LOG_LEVEL if not DEBUG_MODE else 'DEBUG'
    logger.setLevel(getattr(logging, level.upper()))

    # 创建格式化器
    detailed_formatter = MillisecondFormatter(
        '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s'
    )
    simple_formatter = MillisecondFormatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(simple_formatter)

    # 根据日志级别设置控制台输出级别
    console_level = logging.INFO if not DEBUG_MODE else logging.DEBUG
    console_handler.setLevel(console_level)
    logger.addHandler(console_handler)

    # 文件处理器（如果指定了日志文件或有默认配置）
    file_path = log_file or LOG_FILE
    if file_path:
        log_path = Path(file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        # 使用旋转文件处理器
        file_handler = RotatingFileHandler(
            file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(detailed_formatter)
        logger.addHandler(file_handler)

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称
    
    Returns:
        日志器实例
    """
    return setup_logger(name)
