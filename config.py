from pathlib import Path

# 目录配置
DEBUG_DIR = Path("debug_images")
RESULT_DIR = Path("result_images")

# 确保目录存在
DEBUG_DIR.mkdir(exist_ok=True)
RESULT_DIR.mkdir(exist_ok=True)

# 类别名称映射
CUSTOM_NAMES = {
    'hand': 'A_手',
    'bolt': 'B_螺丝', 
    'hammer': 'C_锤子'
}

# 检测参数
DETECTION_CONFIG = {
    'hand_bolt_contact_threshold': 50,  # 手触螺丝距离阈值（像素）
    'hammer_bolt_close_threshold': 80,  # 锤击螺丝接近阈值（像素）
    'hammer_movement_threshold': 100,   # 锤子运动匹配阈值（像素）
    'confidence_threshold': 0.5         # 置信度阈值
}