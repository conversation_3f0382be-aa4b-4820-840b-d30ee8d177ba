import os
from pathlib import Path
from typing import Dict, Any

# 环境变量配置
DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
MODEL_PATH = os.getenv('MODEL_PATH', 'yolo11-obb.pt')
HOST = os.getenv('HOST', '0.0.0.0')
PORT = int(os.getenv('PORT', '5000'))

# 目录配置
DEBUG_DIR = Path(os.getenv('DEBUG_DIR', 'debug_images'))
RESULT_DIR = Path(os.getenv('RESULT_DIR', 'result_images'))

# 确保目录存在
DEBUG_DIR.mkdir(exist_ok=True)
RESULT_DIR.mkdir(exist_ok=True)

# 类别名称映射
CUSTOM_NAMES: Dict[str, str] = {
    'hand': 'A_手',
    'bolt': 'B_螺丝',
    'hammer': 'C_锤子'
}

# 检测参数配置
DETECTION_CONFIG: Dict[str, Any] = {
    'hand_bolt_contact_threshold': int(os.getenv('HAND_BOLT_THRESHOLD', '50')),
    'hammer_bolt_close_threshold': int(os.getenv('HAMMER_BOLT_THRESHOLD', '80')),
    'hammer_movement_threshold': int(os.getenv('HAMMER_MOVEMENT_THRESHOLD', '100')),
    'confidence_threshold': float(os.getenv('CONFIDENCE_THRESHOLD', '0.5')),
    'inference_image_size': int(os.getenv('INFERENCE_IMAGE_SIZE', '960')),
    'max_detections': int(os.getenv('MAX_DETECTIONS', '100')),
    'iou_threshold': float(os.getenv('IOU_THRESHOLD', '0.45')),
    'download_timeout': int(os.getenv('DOWNLOAD_TIMEOUT', '10'))
}

# API配置
API_CONFIG: Dict[str, Any] = {
    'max_image_urls': int(os.getenv('MAX_IMAGE_URLS', '10')),
    'request_timeout': int(os.getenv('REQUEST_TIMEOUT', '30'))
}