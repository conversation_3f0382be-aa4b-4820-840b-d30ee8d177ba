# 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# 应用配置
DEBUG_MODE=False
HOST=0.0.0.0
PORT=5000

# 模型配置
MODEL_PATH=yolo11-obb.pt

# 目录配置
DEBUG_DIR=debug_images
RESULT_DIR=result_images

# 检测参数配置
HAND_BOLT_THRESHOLD=50
HAMMER_BOLT_THRESHOLD=80
HAMMER_MOVEMENT_THRESHOLD=100
CONFIDENCE_THRESHOLD=0.5
INFERENCE_IMAGE_SIZE=960
MAX_DETECTIONS=100
IOU_THRESHOLD=0.45
DOWNLOAD_TIMEOUT=10

# API配置
MAX_IMAGE_URLS=10
REQUEST_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
