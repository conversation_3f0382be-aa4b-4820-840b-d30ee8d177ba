"""
行为分析模块
分析检测到的物体之间的交互行为
"""

from typing import Dict, List, Tuple
import numpy as np

from config import DETECTION_CONFIG
from utils.logger import get_logger
from utils.image_utils import calculate_distance

logger = get_logger(__name__)


class BehaviorAnalyzer:
    """行为分析器 - 分析物体间的交互行为"""
    
    def __init__(self):
        """初始化行为分析器"""
        self.hand_bolt_threshold = DETECTION_CONFIG['hand_bolt_contact_threshold']
        self.hammer_bolt_threshold = DETECTION_CONFIG['hammer_bolt_close_threshold']
        self.hammer_movement_threshold = DETECTION_CONFIG['hammer_movement_threshold']
        
        logger.info("行为分析器初始化完成")
        logger.info(f"手触螺丝阈值: {self.hand_bolt_threshold}px")
        logger.info(f"锤击螺丝阈值: {self.hammer_bolt_threshold}px")
        logger.info(f"锤子运动阈值: {self.hammer_movement_threshold}px")
    
    def check_hand_bolt_contact(self, hands: List[Dict], bolts: List[Dict]) -> Tuple[bool, float, Dict]:
        """
        检测手触螺丝 - 单帧接触即判定
        
        Args:
            hands: 手部检测结果列表
            bolts: 螺丝检测结果列表
            
        Returns:
            tuple: (是否检测到接触, 最高置信度, 最佳匹配详情)
        """
        if not hands or not bolts:
            return False, 0.0, {}
        
        max_confidence = 0.0
        best_match = {}
        contact_detected = False
        
        for hand in hands:
            for bolt in bolts:
                # 计算距离
                distance = calculate_distance(hand['center'], bolt['center'])
                
                if distance <= self.hand_bolt_threshold:
                    contact_detected = True
                    confidence = min(hand['confidence'], bolt['confidence'])
                    
                    if confidence > max_confidence:
                        max_confidence = confidence
                        best_match = {
                            'hand_center': hand['center'],
                            'bolt_center': bolt['center'],
                            'distance': distance,
                            'hand_conf': hand['confidence'],
                            'bolt_conf': bolt['confidence'],
                            'hand_class': hand.get('class_name', '手'),
                            'bolt_class': bolt.get('class_name', '螺丝')
                        }
        
        if contact_detected:
            logger.info(f"检测到手触螺丝，距离: {best_match.get('distance', 0):.1f}px, 置信度: {max_confidence:.3f}")
        
        return contact_detected, max_confidence, best_match
    
    def check_hammer_bolt_action(self, frame_detections: List[Dict]) -> Tuple[bool, float, Dict]:
        """
        检测锤击螺丝 - 基于存在性+运动趋势判定
        
        Args:
            frame_detections: 多帧检测结果列表
            
        Returns:
            tuple: (是否检测到锤击, 最高置信度, 最佳匹配详情)
        """
        if len(frame_detections) < 2:
            logger.debug("帧数不足，无法进行锤击检测")
            return False, 0.0, {}
        
        max_confidence = 0.0
        best_match = {}
        action_detected = False
        
        # 分析连续帧中的锤子和螺丝
        for i in range(len(frame_detections) - 1):
            current_frame = frame_detections[i]
            next_frame = frame_detections[i + 1]
            
            current_hammers = current_frame.get('hammers', [])
            current_bolts = current_frame.get('bolts', [])
            next_hammers = next_frame.get('hammers', [])
            next_bolts = next_frame.get('bolts', [])
            
            if not (current_hammers and current_bolts and next_hammers):
                continue
            
            # 分析锤子和螺丝的交互
            frame_result = self._analyze_hammer_bolt_interaction(
                current_hammers, current_bolts, next_hammers, next_bolts
            )
            
            if frame_result['detected'] and frame_result['confidence'] > max_confidence:
                action_detected = True
                max_confidence = frame_result['confidence']
                best_match = frame_result['details']
        
        if action_detected:
            logger.info(f"检测到锤击螺丝，置信度: {max_confidence:.3f}")
        
        return action_detected, max_confidence, best_match
    
    def _analyze_hammer_bolt_interaction(
        self, 
        current_hammers: List[Dict], 
        current_bolts: List[Dict],
        next_hammers: List[Dict], 
        next_bolts: List[Dict]
    ) -> Dict:
        """
        分析单帧间的锤子螺丝交互
        
        Args:
            current_hammers: 当前帧锤子检测结果
            current_bolts: 当前帧螺丝检测结果
            next_hammers: 下一帧锤子检测结果
            next_bolts: 下一帧螺丝检测结果
            
        Returns:
            分析结果字典
        """
        max_confidence = 0.0
        best_match = {}
        detected = False
        
        # 匹配锤子和螺丝对
        for curr_hammer in current_hammers:
            for curr_bolt in current_bolts:
                curr_distance = calculate_distance(curr_hammer['center'], curr_bolt['center'])
                
                # 查找下一帧中最近的锤子（基于运动连续性）
                best_next_hammer = self._find_matching_hammer(curr_hammer, next_hammers)
                
                if best_next_hammer:
                    next_distance = calculate_distance(best_next_hammer['center'], curr_bolt['center'])
                    
                    # 判定条件：
                    # 1. 距离很近（<阈值像素）
                    # 2. 或者锤子向螺丝移动（距离减小）
                    close_enough = min(curr_distance, next_distance) < self.hammer_bolt_threshold
                    moving_closer = next_distance < curr_distance
                    
                    if close_enough or moving_closer:
                        detected = True
                        confidence = min(curr_hammer['confidence'], curr_bolt['confidence'])
                        
                        if confidence > max_confidence:
                            max_confidence = confidence
                            best_match = {
                                'hammer_center': curr_hammer['center'],
                                'bolt_center': curr_bolt['center'],
                                'initial_distance': curr_distance,
                                'final_distance': next_distance,
                                'movement_direction': 'closer' if moving_closer else 'close',
                                'hammer_conf': curr_hammer['confidence'],
                                'bolt_conf': curr_bolt['confidence'],
                                'hammer_class': curr_hammer.get('class_name', '锤子'),
                                'bolt_class': curr_bolt.get('class_name', '螺丝')
                            }
        
        return {
            'detected': detected,
            'confidence': max_confidence,
            'details': best_match
        }
    
    def _find_matching_hammer(self, current_hammer: Dict, next_hammers: List[Dict]) -> Dict:
        """
        在下一帧中找到匹配的锤子（基于位置连续性）
        
        Args:
            current_hammer: 当前帧的锤子
            next_hammers: 下一帧的锤子列表
            
        Returns:
            匹配的锤子，如果没找到返回None
        """
        min_distance = float('inf')
        best_hammer = None
        
        for next_hammer in next_hammers:
            movement_distance = calculate_distance(
                current_hammer['center'], 
                next_hammer['center']
            )
            
            # 假设同一个锤子在相邻帧间的移动距离不会太大
            if movement_distance < self.hammer_movement_threshold and movement_distance < min_distance:
                min_distance = movement_distance
                best_hammer = next_hammer
        
        return best_hammer
    
    def analyze_batch_behaviors(
        self, 
        frame_detections: List[Dict], 
        detection_types: List[int]
    ) -> Dict:
        """
        批量分析多帧检测结果中的行为
        
        Args:
            frame_detections: 多帧检测结果
            detection_types: 要检测的行为类型列表 (0:手触螺丝, 1:锤击螺丝)
            
        Returns:
            行为分析结果字典
        """
        results = {}
        
        # 手触螺丝检测
        if 0 in detection_types:
            hand_bolt_detected = False
            hand_bolt_confidence = 0.0
            hand_bolt_details = {}
            
            for frame_detection in frame_detections:
                detected, conf, details = self.check_hand_bolt_contact(
                    frame_detection.get('hands', []), 
                    frame_detection.get('bolts', [])
                )
                if detected and conf > hand_bolt_confidence:
                    hand_bolt_detected = True
                    hand_bolt_confidence = conf
                    hand_bolt_details = details
            
            results['hand_bolt_contact'] = {
                'detected': hand_bolt_detected,
                'confidence': hand_bolt_confidence,
                'details': hand_bolt_details
            }
        
        # 锤击螺丝检测
        if 1 in detection_types:
            hammer_detected, hammer_confidence, hammer_details = self.check_hammer_bolt_action(frame_detections)
            
            results['hammer_bolt_action'] = {
                'detected': hammer_detected,
                'confidence': hammer_confidence,
                'details': hammer_details
            }
        
        return results
