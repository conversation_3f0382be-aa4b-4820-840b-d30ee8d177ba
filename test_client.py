"""
测试客户端
用于测试检测服务的各个API接口
"""

import requests
import json
import time
from typing import Dict, List


class DetectionServiceClient:
    """检测服务客户端"""

    def __init__(self, base_url: str = "http://localhost:5000"):
        """
        初始化客户端

        Args:
            base_url: 服务基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'DetectionServiceClient/1.0'
        })

    def health_check(self) -> Dict:
        """健康检查"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def get_api_info(self) -> Dict:
        """获取API信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/info", timeout=10)
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def detect_batch(self, image_urls: List[str], detection_types: List[int] = None) -> Dict:
        """批量检测"""
        if detection_types is None:
            detection_types = [0, 1]

        test_data = {
            "image_urls": image_urls,
            "detection_types": detection_types
        }

        try:
            response = self.session.post(
                f"{self.base_url}/detect",
                json=test_data,
                timeout=60
            )
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def detect_single(self, image_url: str, detection_types: List[int] = None) -> Dict:
        """单张图片检测"""
        if detection_types is None:
            detection_types = [0, 1]

        test_data = {
            "image_url": image_url,
            "detection_types": detection_types
        }

        try:
            response = self.session.post(
                f"{self.base_url}/detect/single",
                json=test_data,
                timeout=30
            )
            return {
                "success": True,
                "status_code": response.status_code,
                "data": response.json()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }


def run_comprehensive_test():
    """运行综合测试"""
    client = DetectionServiceClient()

    print("=" * 60)
    print("CLJX Detection Service 测试开始")
    print("=" * 60)

    # 1. 健康检查
    print("\n1. 健康检查测试")
    print("-" * 30)
    health_result = client.health_check()
    if health_result["success"]:
        print("✓ 健康检查通过")
        print(json.dumps(health_result["data"], indent=2, ensure_ascii=False))
    else:
        print(f"✗ 健康检查失败: {health_result['error']}")
        return

    # 2. API信息
    print("\n2. API信息测试")
    print("-" * 30)
    info_result = client.get_api_info()
    if info_result["success"]:
        print("✓ API信息获取成功")
        print(json.dumps(info_result["data"], indent=2, ensure_ascii=False))
    else:
        print(f"✗ API信息获取失败: {info_result['error']}")

    # 3. 单张图片检测测试
    print("\n3. 单张图片检测测试")
    print("-" * 30)
    single_result = client.detect_single(
        "https://example.com/test_image.jpg",
        [0, 1]
    )
    if single_result["success"]:
        print("✓ 单张图片检测请求成功")
        data = single_result["data"]
        print(f"状态码: {data.get('code', 'N/A')}")
        print(f"消息: {data.get('msg', 'N/A')}")
        print(f"耗时: {data.get('cost_time', 'N/A')}")
    else:
        print(f"✗ 单张图片检测失败: {single_result['error']}")

    # 4. 批量检测测试
    print("\n4. 批量检测测试")
    print("-" * 30)
    test_urls = [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg",
        "https://example.com/image3.jpg"
    ]

    batch_result = client.detect_batch(test_urls, [0, 1])
    if batch_result["success"]:
        print("✓ 批量检测请求成功")
        data = batch_result["data"]
        print(f"状态码: {data.get('code', 'N/A')}")
        print(f"消息: {data.get('msg', 'N/A')}")
        print(f"耗时: {data.get('cost_time', 'N/A')}")
        print(f"处理图片数: {data.get('processed_images', 'N/A')}")
    else:
        print(f"✗ 批量检测失败: {batch_result['error']}")

    # 5. 错误处理测试
    print("\n5. 错误处理测试")
    print("-" * 30)

    # 测试无效URL
    invalid_result = client.detect_single("invalid_url", [0])
    if invalid_result["success"]:
        data = invalid_result["data"]
        print(f"无效URL测试 - 状态码: {data.get('code', 'N/A')}, 消息: {data.get('msg', 'N/A')}")

    # 测试空列表
    empty_result = client.detect_batch([], [0, 1])
    if empty_result["success"]:
        data = empty_result["data"]
        print(f"空列表测试 - 状态码: {data.get('code', 'N/A')}, 消息: {data.get('msg', 'N/A')}")

    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


def test_detection_service():
    """简单的检测服务测试（保持向后兼容）"""
    client = DetectionServiceClient()

    # 测试数据
    test_urls = [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg",
        "https://example.com/image3.jpg"
    ]

    try:
        result = client.detect_batch(test_urls, [0, 1])

        if result["success"]:
            print("检测结果:")
            print(json.dumps(result["data"], indent=2, ensure_ascii=False))
        else:
            print(f"测试失败: {result['error']}")

    except Exception as e:
        print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    # 运行综合测试
    run_comprehensive_test()

    # 也可以运行简单测试
    # test_detection_service()