import requests
import json

def test_detection_service():
    """测试检测服务"""
    url = "http://localhost:5000/detect"
    
    # 测试数据
    test_data = {
        "image_urls": [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg"
        ],
        "detection_types": [0, 1]  # 0:手触螺丝, 1:锤击螺丝
    }
    
    try:
        response = requests.post(url, json=test_data, timeout=30)
        result = response.json()
        
        print("检测结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    test_detection_service()