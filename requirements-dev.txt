# 开发和测试依赖

# 测试框架
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0

# 代码质量检查
flake8>=6.0.0
mypy>=1.0.0
bandit>=1.7.0
safety>=2.3.0

# 代码格式化
black>=23.0.0
isort>=5.12.0
autopep8>=2.0.0

# 文档生成
sphinx>=5.0.0
sphinx-rtd-theme>=1.2.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=4.0.0
py-spy>=0.3.0

# 开发工具
ipython>=8.0.0
jupyter>=1.0.0
pre-commit>=3.0.0

# HTTP客户端（用于测试）
httpx>=0.24.0
aiohttp>=3.8.0

# 数据分析（可选）
pandas>=1.5.0
matplotlib>=3.6.0
seaborn>=0.12.0

# 容器化工具
docker>=6.0.0
docker-compose>=1.29.0
