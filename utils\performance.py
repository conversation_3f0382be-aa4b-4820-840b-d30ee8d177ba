"""
性能监控和优化工具模块
"""

import time
import psutil
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from collections import deque
import numpy as np

from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    gpu_memory_used_mb: Optional[float]
    inference_time: Optional[float]
    processing_time: Optional[float]
    timestamp: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 100):
        """
        初始化性能监控器
        
        Args:
            max_history: 保存的历史记录最大数量
        """
        self.max_history = max_history
        self.metrics_history = deque(maxlen=max_history)
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.Lock()
        
        # 检查GPU可用性
        self.gpu_available = self._check_gpu_availability()
        
        logger.info(f"性能监控器初始化完成，GPU可用: {self.gpu_available}")
    
    def _check_gpu_availability(self) -> bool:
        """检查GPU是否可用"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前性能指标"""
        # CPU和内存使用率
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / 1024 / 1024
        
        # GPU内存使用（如果可用）
        gpu_memory_used_mb = None
        if self.gpu_available:
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_memory_used_mb = torch.cuda.memory_allocated() / 1024 / 1024
            except Exception as e:
                logger.debug(f"获取GPU内存使用失败: {str(e)}")
        
        return PerformanceMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            gpu_memory_used_mb=gpu_memory_used_mb,
            inference_time=None,
            processing_time=None,
            timestamp=time.time()
        )
    
    def record_metrics(self, 
                      inference_time: Optional[float] = None,
                      processing_time: Optional[float] = None):
        """
        记录性能指标
        
        Args:
            inference_time: 推理时间（秒）
            processing_time: 处理时间（秒）
        """
        metrics = self.get_current_metrics()
        metrics.inference_time = inference_time
        metrics.processing_time = processing_time
        
        with self._lock:
            self.metrics_history.append(metrics)
    
    def get_statistics(self, last_n: Optional[int] = None) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Args:
            last_n: 统计最近N条记录，None表示所有记录
            
        Returns:
            统计信息字典
        """
        with self._lock:
            if not self.metrics_history:
                return {}
            
            # 获取指定数量的记录
            if last_n is None:
                metrics_list = list(self.metrics_history)
            else:
                metrics_list = list(self.metrics_history)[-last_n:]
            
            if not metrics_list:
                return {}
            
            # 计算统计信息
            cpu_values = [m.cpu_percent for m in metrics_list]
            memory_values = [m.memory_percent for m in metrics_list]
            memory_mb_values = [m.memory_used_mb for m in metrics_list]
            
            stats = {
                "record_count": len(metrics_list),
                "time_range": {
                    "start": metrics_list[0].timestamp,
                    "end": metrics_list[-1].timestamp,
                    "duration": metrics_list[-1].timestamp - metrics_list[0].timestamp
                },
                "cpu": {
                    "avg": np.mean(cpu_values),
                    "min": np.min(cpu_values),
                    "max": np.max(cpu_values),
                    "std": np.std(cpu_values)
                },
                "memory": {
                    "percent": {
                        "avg": np.mean(memory_values),
                        "min": np.min(memory_values),
                        "max": np.max(memory_values),
                        "std": np.std(memory_values)
                    },
                    "used_mb": {
                        "avg": np.mean(memory_mb_values),
                        "min": np.min(memory_mb_values),
                        "max": np.max(memory_mb_values),
                        "std": np.std(memory_mb_values)
                    }
                }
            }
            
            # GPU统计（如果有数据）
            gpu_values = [m.gpu_memory_used_mb for m in metrics_list if m.gpu_memory_used_mb is not None]
            if gpu_values:
                stats["gpu_memory_mb"] = {
                    "avg": np.mean(gpu_values),
                    "min": np.min(gpu_values),
                    "max": np.max(gpu_values),
                    "std": np.std(gpu_values)
                }
            
            # 推理时间统计
            inference_times = [m.inference_time for m in metrics_list if m.inference_time is not None]
            if inference_times:
                stats["inference_time"] = {
                    "avg": np.mean(inference_times),
                    "min": np.min(inference_times),
                    "max": np.max(inference_times),
                    "std": np.std(inference_times),
                    "count": len(inference_times)
                }
            
            # 处理时间统计
            processing_times = [m.processing_time for m in metrics_list if m.processing_time is not None]
            if processing_times:
                stats["processing_time"] = {
                    "avg": np.mean(processing_times),
                    "min": np.min(processing_times),
                    "max": np.max(processing_times),
                    "std": np.std(processing_times),
                    "count": len(processing_times)
                }
            
            return stats
    
    def start_monitoring(self, interval: float = 5.0):
        """
        开始后台监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self._monitoring:
            logger.warning("性能监控已在运行")
            return
        
        self._monitoring = True
        
        def monitor_loop():
            while self._monitoring:
                try:
                    self.record_metrics()
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"性能监控循环出错: {str(e)}")
                    time.sleep(interval)
        
        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        logger.info(f"性能监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止后台监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
        
        logger.info("性能监控已停止")
    
    def clear_history(self):
        """清空历史记录"""
        with self._lock:
            self.metrics_history.clear()
        logger.info("性能监控历史记录已清空")


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, name: str):
        """
        初始化性能分析器
        
        Args:
            name: 分析器名称
        """
        self.name = name
        self.start_time = None
        self.end_time = None
        self.checkpoints = []
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        self.checkpoints = []
        logger.debug(f"性能分析开始: {self.name}")
    
    def checkpoint(self, label: str):
        """添加检查点"""
        if self.start_time is None:
            logger.warning("性能分析未开始，无法添加检查点")
            return
        
        current_time = time.time()
        elapsed = current_time - self.start_time
        self.checkpoints.append((label, elapsed, current_time))
        logger.debug(f"检查点 {label}: {elapsed:.3f}秒")
    
    def end(self) -> Dict[str, Any]:
        """结束计时并返回结果"""
        if self.start_time is None:
            logger.warning("性能分析未开始")
            return {}
        
        self.end_time = time.time()
        total_time = self.end_time - self.start_time
        
        result = {
            "name": self.name,
            "total_time": total_time,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "checkpoints": self.checkpoints
        }
        
        logger.info(f"性能分析完成: {self.name}, 总耗时: {total_time:.3f}秒")
        
        # 输出详细的检查点信息
        if self.checkpoints:
            logger.debug(f"检查点详情 ({self.name}):")
            prev_time = 0
            for label, elapsed, timestamp in self.checkpoints:
                interval = elapsed - prev_time
                logger.debug(f"  {label}: +{interval:.3f}s (累计: {elapsed:.3f}s)")
                prev_time = elapsed
        
        return result


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def profile_function(name: Optional[str] = None):
    """
    函数性能分析装饰器
    
    Args:
        name: 分析器名称，默认使用函数名
    """
    def decorator(func: Callable) -> Callable:
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profiler_name = name or f"{func.__module__}.{func.__name__}"
            profiler = PerformanceProfiler(profiler_name)
            
            profiler.start()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                profiler.end()
        
        return wrapper
    return decorator
