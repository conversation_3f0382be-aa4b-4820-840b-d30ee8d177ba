"""
主应用入口文件
启动Flask应用服务
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.routes import create_app
from config import HOST, PORT, DEBUG_MODE
from utils.logger import get_logger

logger = get_logger(__name__)


def main():
    """主函数 - 启动Flask应用"""
    try:
        # 创建Flask应用
        app = create_app()
        
        logger.info("=" * 50)
        logger.info("CLJX Detection Service 启动中...")
        logger.info(f"主机: {HOST}")
        logger.info(f"端口: {PORT}")
        logger.info(f"调试模式: {DEBUG_MODE}")
        logger.info("=" * 50)
        
        # 启动应用
        app.run(
            host=HOST,
            port=PORT,
            debug=DEBUG_MODE,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"应用启动失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
