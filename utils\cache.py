"""
缓存模块
提供内存缓存和文件缓存功能
"""

import time
import pickle
import hashlib
import threading
from pathlib import Path
from typing import Any, Optional, Dict, Callable
from dataclasses import dataclass

from utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    timestamp: float
    ttl: float
    access_count: int = 0
    last_access: float = 0.0


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认生存时间（秒）
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        
        logger.info(f"内存缓存初始化，最大条目数: {max_size}, 默认TTL: {default_ttl}秒")
    
    def _generate_key(self, key: Any) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        else:
            # 对复杂对象生成哈希键
            key_str = str(key)
            return hashlib.md5(key_str.encode()).hexdigest()
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存条目是否过期"""
        return time.time() - entry.timestamp > entry.ttl
    
    def _evict_expired(self):
        """清理过期条目"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if current_time - entry.timestamp > entry.ttl
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    def _evict_lru(self):
        """清理最少使用的条目"""
        if len(self._cache) <= self.max_size:
            return
        
        # 按最后访问时间排序，删除最旧的条目
        sorted_items = sorted(
            self._cache.items(),
            key=lambda x: x[1].last_access or x[1].timestamp
        )
        
        # 删除最旧的条目直到达到最大大小
        while len(self._cache) > self.max_size:
            key_to_remove = sorted_items.pop(0)[0]
            del self._cache[key_to_remove]
            logger.debug(f"LRU清理缓存条目: {key_to_remove}")
    
    def get(self, key: Any, default: Any = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            
        Returns:
            缓存值或默认值
        """
        cache_key = self._generate_key(key)
        
        with self._lock:
            if cache_key not in self._cache:
                return default
            
            entry = self._cache[cache_key]
            
            # 检查是否过期
            if self._is_expired(entry):
                del self._cache[cache_key]
                logger.debug(f"缓存条目已过期: {cache_key}")
                return default
            
            # 更新访问统计
            entry.access_count += 1
            entry.last_access = time.time()
            
            logger.debug(f"缓存命中: {cache_key}")
            return entry.value
    
    def set(self, key: Any, value: Any, ttl: Optional[float] = None):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒），None使用默认值
        """
        cache_key = self._generate_key(key)
        ttl = ttl or self.default_ttl
        
        with self._lock:
            # 清理过期条目
            self._evict_expired()
            
            # 创建新的缓存条目
            entry = CacheEntry(
                value=value,
                timestamp=time.time(),
                ttl=ttl,
                access_count=1,
                last_access=time.time()
            )
            
            self._cache[cache_key] = entry
            
            # 如果超过最大大小，执行LRU清理
            self._evict_lru()
            
            logger.debug(f"缓存设置: {cache_key}, TTL: {ttl}秒")
    
    def delete(self, key: Any) -> bool:
        """
        删除缓存条目
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        cache_key = self._generate_key(key)
        
        with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                logger.debug(f"缓存删除: {cache_key}")
                return True
            return False
    
    def clear(self):
        """清空所有缓存"""
        with self._lock:
            count = len(self._cache)
            self._cache.clear()
            logger.info(f"清空了 {count} 个缓存条目")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_entries = len(self._cache)
            total_access = sum(entry.access_count for entry in self._cache.values())
            
            return {
                "total_entries": total_entries,
                "max_size": self.max_size,
                "usage_ratio": total_entries / self.max_size if self.max_size > 0 else 0,
                "total_access": total_access,
                "avg_access_per_entry": total_access / total_entries if total_entries > 0 else 0
            }


class FileCache:
    """文件缓存"""
    
    def __init__(self, cache_dir: str = "cache", default_ttl: float = 3600):
        """
        初始化文件缓存
        
        Args:
            cache_dir: 缓存目录
            default_ttl: 默认生存时间（秒）
        """
        self.cache_dir = Path(cache_dir)
        self.default_ttl = default_ttl
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info(f"文件缓存初始化，目录: {self.cache_dir}, 默认TTL: {default_ttl}秒")
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用哈希避免文件名过长或包含特殊字符
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def _is_expired(self, file_path: Path, ttl: float) -> bool:
        """检查文件是否过期"""
        if not file_path.exists():
            return True
        
        file_time = file_path.stat().st_mtime
        return time.time() - file_time > ttl
    
    def get(self, key: str, default: Any = None, ttl: Optional[float] = None) -> Any:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
            ttl: 生存时间检查
            
        Returns:
            缓存值或默认值
        """
        cache_path = self._get_cache_path(key)
        ttl = ttl or self.default_ttl
        
        try:
            if self._is_expired(cache_path, ttl):
                logger.debug(f"文件缓存过期: {key}")
                return default
            
            with open(cache_path, 'rb') as f:
                value = pickle.load(f)
            
            logger.debug(f"文件缓存命中: {key}")
            return value
            
        except Exception as e:
            logger.debug(f"读取文件缓存失败 {key}: {str(e)}")
            return default
    
    def set(self, key: str, value: Any):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
        """
        cache_path = self._get_cache_path(key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(value, f)
            
            logger.debug(f"文件缓存设置: {key}")
            
        except Exception as e:
            logger.error(f"写入文件缓存失败 {key}: {str(e)}")
    
    def delete(self, key: str) -> bool:
        """
        删除缓存文件
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        cache_path = self._get_cache_path(key)
        
        try:
            if cache_path.exists():
                cache_path.unlink()
                logger.debug(f"文件缓存删除: {key}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"删除文件缓存失败 {key}: {str(e)}")
            return False
    
    def clear(self):
        """清空所有缓存文件"""
        try:
            count = 0
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
                count += 1
            
            logger.info(f"清空了 {count} 个文件缓存")
            
        except Exception as e:
            logger.error(f"清空文件缓存失败: {str(e)}")


# 全局缓存实例
memory_cache = MemoryCache()
file_cache = FileCache()


def cached(ttl: float = 300, use_file_cache: bool = False):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存生存时间（秒）
        use_file_cache: 是否使用文件缓存
    """
    def decorator(func: Callable) -> Callable:
        import functools
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__module__}.{func.__name__}:{str(args)}:{str(sorted(kwargs.items()))}"
            
            # 选择缓存类型
            cache = file_cache if use_file_cache else memory_cache
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl if not use_file_cache else None)
            
            return result
        
        return wrapper
    return decorator
