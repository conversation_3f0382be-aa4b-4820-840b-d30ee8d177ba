"""
装饰器模块
提供各种功能装饰器，如错误处理、性能监控等
"""

import time
import functools
from typing import Callable, Any, Dict
from datetime import datetime

from utils.logger import get_logger
from utils.exceptions import DetectionServiceError

logger = get_logger(__name__)


def handle_exceptions(default_return=None, log_error=True):
    """
    异常处理装饰器
    
    Args:
        default_return: 发生异常时的默认返回值
        log_error: 是否记录错误日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except DetectionServiceError as e:
                if log_error:
                    logger.error(f"{func.__name__} 发生业务异常: {e.message} (错误码: {e.error_code})")
                return default_return
            except Exception as e:
                if log_error:
                    logger.error(f"{func.__name__} 发生未知异常: {str(e)}", exc_info=True)
                return default_return
        return wrapper
    return decorator


def measure_time(log_result=True, threshold_seconds=None):
    """
    性能监控装饰器
    
    Args:
        log_result: 是否记录执行时间
        threshold_seconds: 超过此阈值时记录警告日志
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_time = time.time() - start_time
                
                if log_result:
                    if threshold_seconds and execution_time > threshold_seconds:
                        logger.warning(f"{func.__name__} 执行时间过长: {execution_time:.3f}秒")
                    else:
                        logger.debug(f"{func.__name__} 执行时间: {execution_time:.3f}秒")
        return wrapper
    return decorator


def retry(max_attempts=3, delay=1.0, backoff=2.0, exceptions=(Exception,)):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟时间倍数
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"{func.__name__} 重试{max_attempts}次后仍然失败: {str(e)}")
                        raise
                    
                    logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败，{current_delay:.1f}秒后重试: {str(e)}")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return None
        return wrapper
    return decorator


def validate_input(**validators):
    """
    输入验证装饰器
    
    Args:
        **validators: 参数验证器字典，键为参数名，值为验证函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数签名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not validator(value):
                        from utils.exceptions import ValidationError
                        raise ValidationError(f"参数 {param_name} 验证失败", param_name)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator


def cache_result(ttl_seconds=300):
    """
    结果缓存装饰器
    
    Args:
        ttl_seconds: 缓存生存时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = str(args) + str(sorted(kwargs.items()))
            current_time = time.time()
            
            # 检查缓存
            if cache_key in cache:
                cached_result, cached_time = cache[cache_key]
                if current_time - cached_time < ttl_seconds:
                    logger.debug(f"{func.__name__} 使用缓存结果")
                    return cached_result
                else:
                    # 缓存过期，删除
                    del cache[cache_key]
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[cache_key] = (result, current_time)
            
            return result
        return wrapper
    return decorator


def log_function_call(include_args=False, include_result=False):
    """
    函数调用日志装饰器
    
    Args:
        include_args: 是否记录函数参数
        include_result: 是否记录函数返回值
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            
            # 记录函数调用开始
            log_msg = f"调用函数 {func_name}"
            if include_args:
                log_msg += f" 参数: args={args}, kwargs={kwargs}"
            logger.debug(log_msg)
            
            try:
                result = func(*args, **kwargs)
                
                # 记录函数调用成功
                success_msg = f"函数 {func_name} 执行成功"
                if include_result:
                    success_msg += f" 返回值: {result}"
                logger.debug(success_msg)
                
                return result
                
            except Exception as e:
                logger.error(f"函数 {func_name} 执行失败: {str(e)}")
                raise
                
        return wrapper
    return decorator


def rate_limit(calls_per_second=10):
    """
    速率限制装饰器
    
    Args:
        calls_per_second: 每秒允许的调用次数
    """
    def decorator(func: Callable) -> Callable:
        min_interval = 1.0 / calls_per_second
        last_called = [0.0]
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_time = time.time()
            elapsed = current_time - last_called[0]
            
            if elapsed < min_interval:
                sleep_time = min_interval - elapsed
                logger.debug(f"{func.__name__} 速率限制，等待 {sleep_time:.3f} 秒")
                time.sleep(sleep_time)
            
            last_called[0] = time.time()
            return func(*args, **kwargs)
            
        return wrapper
    return decorator
