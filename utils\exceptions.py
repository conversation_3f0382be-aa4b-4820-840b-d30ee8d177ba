"""
自定义异常类模块
定义项目中使用的各种异常类型
"""


class DetectionServiceError(Exception):
    """检测服务基础异常类"""
    
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code or "DETECTION_ERROR"
        super().__init__(self.message)


class ModelLoadError(DetectionServiceError):
    """模型加载异常"""
    
    def __init__(self, message: str, model_path: str = None):
        self.model_path = model_path
        super().__init__(message, "MODEL_LOAD_ERROR")


class ImageDownloadError(DetectionServiceError):
    """图像下载异常"""
    
    def __init__(self, message: str, url: str = None):
        self.url = url
        super().__init__(message, "IMAGE_DOWNLOAD_ERROR")


class ImageProcessingError(DetectionServiceError):
    """图像处理异常"""
    
    def __init__(self, message: str, image_info: str = None):
        self.image_info = image_info
        super().__init__(message, "IMAGE_PROCESSING_ERROR")


class InferenceError(DetectionServiceError):
    """推理异常"""
    
    def __init__(self, message: str, model_info: str = None):
        self.model_info = model_info
        super().__init__(message, "INFERENCE_ERROR")


class ValidationError(DetectionServiceError):
    """参数验证异常"""
    
    def __init__(self, message: str, field_name: str = None):
        self.field_name = field_name
        super().__init__(message, "VALIDATION_ERROR")


class ConfigurationError(DetectionServiceError):
    """配置异常"""
    
    def __init__(self, message: str, config_key: str = None):
        self.config_key = config_key
        super().__init__(message, "CONFIGURATION_ERROR")


class ServiceUnavailableError(DetectionServiceError):
    """服务不可用异常"""
    
    def __init__(self, message: str, service_name: str = None):
        self.service_name = service_name
        super().__init__(message, "SERVICE_UNAVAILABLE")


class ResourceExhaustedError(DetectionServiceError):
    """资源耗尽异常"""
    
    def __init__(self, message: str, resource_type: str = None):
        self.resource_type = resource_type
        super().__init__(message, "RESOURCE_EXHAUSTED")


class TimeoutError(DetectionServiceError):
    """超时异常"""
    
    def __init__(self, message: str, timeout_value: float = None):
        self.timeout_value = timeout_value
        super().__init__(message, "TIMEOUT_ERROR")
