"""
图像工具模块测试
"""

import unittest
import numpy as np
import tempfile
from pathlib import Path
from unittest.mock import patch, Mock
from datetime import datetime

import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.image_utils import (
    calculate_distance,
    validate_image_urls,
    generate_filenames,
    save_image
)


class TestImageUtils(unittest.TestCase):
    """图像工具测试类"""
    
    def test_calculate_distance(self):
        """测试距离计算"""
        # 测试相同点
        self.assertEqual(calculate_distance((0, 0), (0, 0)), 0.0)
        
        # 测试简单距离
        self.assertEqual(calculate_distance((0, 0), (3, 4)), 5.0)
        
        # 测试负坐标
        self.assertEqual(calculate_distance((-1, -1), (2, 3)), 5.0)
        
        # 测试浮点数
        distance = calculate_distance((1.5, 2.5), (4.5, 6.5))
        self.assertAlmostEqual(distance, 5.0, places=5)
    
    def test_validate_image_urls_valid(self):
        """测试有效URL验证"""
        # 有效的URL列表
        valid_urls = [
            "http://example.com/image1.jpg",
            "https://example.com/image2.png"
        ]
        is_valid, error_msg = validate_image_urls(valid_urls)
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
    
    def test_validate_image_urls_invalid(self):
        """测试无效URL验证"""
        # 非列表类型
        is_valid, error_msg = validate_image_urls("not_a_list")
        self.assertFalse(is_valid)
        self.assertIn("必须是列表类型", error_msg)
        
        # 空列表
        is_valid, error_msg = validate_image_urls([])
        self.assertFalse(is_valid)
        self.assertIn("不能为空", error_msg)
        
        # 无效URL格式
        invalid_urls = ["invalid_url"]
        is_valid, error_msg = validate_image_urls(invalid_urls)
        self.assertFalse(is_valid)
        self.assertIn("URL格式无效", error_msg)
        
        # 空字符串URL
        empty_urls = [""]
        is_valid, error_msg = validate_image_urls(empty_urls)
        self.assertFalse(is_valid)
        self.assertIn("不能为空", error_msg)
    
    def test_generate_filenames(self):
        """测试文件名生成"""
        request_time = datetime(2023, 12, 1, 10, 30, 45, 123456)
        session_id = "test-session-id-12345"
        
        filenames = generate_filenames(request_time, session_id)
        
        # 检查返回的键
        expected_keys = ["original", "result", "original_name", "result_name"]
        for key in expected_keys:
            self.assertIn(key, filenames)
        
        # 检查文件名格式
        self.assertTrue(filenames["original_name"].startswith("original_test-ses"))
        self.assertTrue(filenames["result_name"].startswith("result_test-ses"))
        self.assertTrue(filenames["original_name"].endswith(".jpg"))
        self.assertTrue(filenames["result_name"].endswith(".jpg"))
    
    def test_save_image(self):
        """测试图像保存"""
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试成功保存
            filepath = Path(temp_dir) / "test_image.jpg"
            result = save_image(test_image, filepath)
            
            self.assertTrue(result)
            self.assertTrue(filepath.exists())
            
            # 测试保存到不存在的目录
            nested_path = Path(temp_dir) / "nested" / "dir" / "test_image.jpg"
            result = save_image(test_image, nested_path)
            
            self.assertTrue(result)
            self.assertTrue(nested_path.exists())


class TestImageUtilsIntegration(unittest.TestCase):
    """图像工具集成测试"""
    
    @patch('utils.image_utils.requests.get')
    def test_download_image_success(self, mock_get):
        """测试图像下载成功"""
        # 模拟成功的HTTP响应
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        
        # 创建一个简单的图像数据
        import cv2
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        _, encoded_image = cv2.imencode('.jpg', test_image)
        mock_response.content = encoded_image.tobytes()
        
        mock_get.return_value = mock_response
        
        from utils.image_utils import download_image
        result = download_image("http://example.com/test.jpg")
        
        self.assertIsNotNone(result)
        self.assertEqual(result.shape, (100, 100, 3))
    
    @patch('utils.image_utils.requests.get')
    def test_download_image_failure(self, mock_get):
        """测试图像下载失败"""
        # 模拟HTTP错误
        mock_get.side_effect = Exception("Network error")
        
        from utils.image_utils import download_image
        result = download_image("http://example.com/test.jpg")
        
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main()
