"""
实时检测服务模块
提供批量图像检测和行为分析功能
"""

import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional

from config import MODEL_PATH
from models.object_detector import ObjectDetector
from models.behavior_analyzer import BehaviorAnalyzer
from utils.logger import get_logger
from utils.image_utils import download_image, validate_image_urls

logger = get_logger(__name__)


class RealTimeDetectionService:
    """实时检测服务 - 适配头戴/手持设备推流场景"""
    
    def __init__(self, model_path: str = None):
        """
        初始化检测服务
        
        Args:
            model_path: YOLO模型路径
        """
        model_path = model_path or MODEL_PATH
        
        try:
            self.detector = ObjectDetector(model_path)
            self.behavior_analyzer = BehaviorAnalyzer()
            logger.info("实时检测服务初始化完成")
        except Exception as e:
            logger.error(f"检测服务初始化失败: {str(e)}")
            raise
    
    def process_batch(
        self, 
        image_urls: List[str], 
        detection_types: Optional[List[int]] = None
    ) -> Dict:
        """
        处理批量图片检测
        
        Args:
            image_urls: 图片URL列表
            detection_types: 检测类型列表 (0:手触螺丝, 1:锤击螺丝)
            
        Returns:
            检测结果字典
        """
        request_time = datetime.now()
        session_id = str(uuid.uuid4())
        
        logger.info(f"开始处理批量检测，会话ID: {session_id[:8]}, 图片数量: {len(image_urls)}")
        
        # 验证输入参数
        is_valid, error_msg = validate_image_urls(image_urls)
        if not is_valid:
            return self._create_error_response(error_msg, request_time)
        
        if detection_types is None:
            detection_types = [0, 1]  # 默认检测所有类型
        
        # 下载并检测所有图片
        frame_detections = []
        debug_images = []
        inference_start = time.time()
        
        for i, url in enumerate(image_urls):
            try:
                frame = download_image(url)
                if frame is None:
                    logger.warning(f"跳过无法下载的图片: {url}")
                    continue
                
                detections, original_name, result_name = self.detector.detect(frame, session_id)
                frame_detections.append(detections)
                
                if result_name:
                    # 这里应该根据实际部署环境配置域名
                    debug_images.append(f"http://your-domain.com/debug/{result_name}")
                    
            except Exception as e:
                logger.error(f"处理第{i+1}张图片时发生错误 {url}: {str(e)}")
                continue
        
        inference_time = time.time() - inference_start
        
        if not frame_detections:
            return self._create_error_response("没有成功处理任何图片", request_time)
        
        # 分析检测结果
        try:
            behavior_results = self.behavior_analyzer.analyze_batch_behaviors(
                frame_detections, detection_types
            )
        except Exception as e:
            logger.error(f"行为分析失败: {str(e)}")
            behavior_results = {}
        
        # 构建响应
        end_time = datetime.now()
        cost_time = (end_time - request_time).total_seconds()
        
        response = {
            "code": 200,
            "msg": "success",
            "cost_time": f"{cost_time:.3f}秒",
            "request_time": request_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "inference_time": f"{inference_time:.3f}秒",
            "debug_image_url_list": debug_images,
            "source_url": image_urls,
            "processed_images": len(frame_detections),
            "data": behavior_results
        }
        
        logger.info(f"批量检测完成，会话ID: {session_id[:8]}, 总耗时: {cost_time:.3f}秒")
        return response
    
    def process_single_image(
        self, 
        image_url: str, 
        detection_types: Optional[List[int]] = None
    ) -> Dict:
        """
        处理单张图片检测
        
        Args:
            image_url: 图片URL
            detection_types: 检测类型列表
            
        Returns:
            检测结果字典
        """
        return self.process_batch([image_url], detection_types)
    
    def _create_error_response(self, error_msg: str, request_time: datetime) -> Dict:
        """
        创建错误响应
        
        Args:
            error_msg: 错误信息
            request_time: 请求时间
            
        Returns:
            错误响应字典
        """
        end_time = datetime.now()
        cost_time = (end_time - request_time).total_seconds()
        
        return {
            "code": 500,
            "msg": error_msg,
            "cost_time": f"{cost_time:.3f}秒",
            "request_time": request_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "inference_time": "0.000秒",
            "debug_image_url_list": [],
            "source_url": [],
            "processed_images": 0,
            "data": {}
        }
    
    def get_service_status(self) -> Dict:
        """
        获取服务状态信息
        
        Returns:
            服务状态字典
        """
        try:
            # 简单的健康检查
            import torch
            gpu_available = torch.cuda.is_available()
            gpu_count = torch.cuda.device_count() if gpu_available else 0
            
            return {
                "status": "healthy",
                "model_loaded": self.detector.model is not None,
                "gpu_available": gpu_available,
                "gpu_count": gpu_count,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            logger.error(f"获取服务状态失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
