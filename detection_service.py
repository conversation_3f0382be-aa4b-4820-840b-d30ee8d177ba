from flask import Flask, request, jsonify
import cv2
import numpy as np
from ultralytics import YOLO
import requests
import time
import logging
import uuid
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import math
from pathlib import Path

# 配置日志格式，包含毫秒
class MillisecondFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        ct = self.converter(record.created)
        if datefmt:
            s = datetime.fromtimestamp(record.created).strftime(datefmt)
        else:
            t = datetime.fromtimestamp(record.created)
            s = t.strftime('%H:%M:%S.%f')[:-3]  # 保留3位毫秒
        return s
# 设置日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
for handler in logging.root.handlers:
    handler.setFormatter(MillisecondFormatter('%(asctime)s [%(levelname)s] %(name)s: %(message)s'))

logger = logging.getLogger(__name__)

app = Flask(__name__)

class RealTimeDetectionService:
    """实时检测服务 - 适配头戴/手持设备推流场景"""
    
    def __init__(self, model_path='yolo11-obb.pt'):
        self.detector = ObjectDetector(model_path)
        logger.info("实时检测服务初始化完成")
    
    def download_image(self, url: str) -> Optional[np.ndarray]:
        """下载在线图片"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # 转换为OpenCV格式
            img_array = np.frombuffer(response.content, np.uint8)
            frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            
            if frame is None:
                logger.error(f"无法解码图片: {url}")
                return None
                
            logger.info(f"成功下载图片: {url}, 尺寸: {frame.shape}")
            return frame
            
        except Exception as e:
            logger.error(f"下载图片失败 {url}: {str(e)}")
            return None
    
    def calculate_distance(self, center1: List[float], center2: List[float]) -> float:
        """计算两个中心点之间的距离"""
        return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
    
    def check_hand_bolt_contact(self, hands: List[Dict], bolts: List[Dict]) -> Tuple[bool, float, Dict]:
        """检测手触螺丝 - 单帧接触即判定"""
        if not hands or not bolts:
            return False, 0.0, {}
        
        max_confidence = 0.0
        best_match = {}
        contact_detected = False
        
        for hand in hands:
            for bolt in bolts:
                # 计算距离
                distance = self.calculate_distance(hand['center'], bolt['center'])
                
                # 接触判定阈值（像素）
                contact_threshold = 50
                
                if distance <= contact_threshold:
                    contact_detected = True
                    confidence = min(hand['confidence'], bolt['confidence'])
                    
                    if confidence > max_confidence:
                        max_confidence = confidence
                        best_match = {
                            'hand_center': hand['center'],
                            'bolt_center': bolt['center'],
                            'distance': distance,
                            'hand_conf': hand['confidence'],
                            'bolt_conf': bolt['confidence']
                        }
        
        return contact_detected, max_confidence, best_match
    
    def check_hammer_bolt_action(self, frame_detections: List[Dict]) -> Tuple[bool, float, Dict]:
        """检测锤击螺丝 - 基于存在性+运动趋势判定"""
        if len(frame_detections) < 2:
            return False, 0.0, {}
        
        max_confidence = 0.0
        best_match = {}
        action_detected = False
        
        # 分析连续帧中的锤子和螺丝
        for i in range(len(frame_detections) - 1):
            current_frame = frame_detections[i]
            next_frame = frame_detections[i + 1]
            
            current_hammers = current_frame.get('hammers', [])
            current_bolts = current_frame.get('bolts', [])
            next_hammers = next_frame.get('hammers', [])
            next_bolts = next_frame.get('bolts', [])
            
            if not (current_hammers and current_bolts and next_hammers):
                continue
            
            # 匹配锤子和螺丝对
            for curr_hammer in current_hammers:
                for curr_bolt in current_bolts:
                    curr_distance = self.calculate_distance(curr_hammer['center'], curr_bolt['center'])
                    
                    # 查找下一帧中最近的锤子
                    min_next_distance = float('inf')
                    best_next_hammer = None
                    
                    for next_hammer in next_hammers:
                        hammer_movement = self.calculate_distance(curr_hammer['center'], next_hammer['center'])
                        if hammer_movement < 100:  # 假设同一个锤子
                            next_distance = self.calculate_distance(next_hammer['center'], curr_bolt['center'])
                            if next_distance < min_next_distance:
                                min_next_distance = next_distance
                                best_next_hammer = next_hammer
                    
                    if best_next_hammer:
                        # 判定条件：
                        # 1. 距离很近（<80像素）
                        # 2. 或者锤子向螺丝移动（距离减小）
                        close_enough = min(curr_distance, min_next_distance) < 80
                        moving_closer = min_next_distance < curr_distance
                        
                        if close_enough or moving_closer:
                            action_detected = True
                            confidence = min(curr_hammer['confidence'], curr_bolt['confidence'])
                            
                            if confidence > max_confidence:
                                max_confidence = confidence
                                best_match = {
                                    'hammer_center': curr_hammer['center'],
                                    'bolt_center': curr_bolt['center'],
                                    'initial_distance': curr_distance,
                                    'final_distance': min_next_distance,
                                    'movement_direction': 'closer' if moving_closer else 'close',
                                    'hammer_conf': curr_hammer['confidence'],
                                    'bolt_conf': curr_bolt['confidence']
                                }
        
        return action_detected, max_confidence, best_match
    
    def process_batch(self, image_urls: List[str], detection_types: List[int] = None) -> Dict:
        """处理批量图片检测"""
        request_time = datetime.now()
        session_id = str(uuid.uuid4())
        
        logger.info(f"开始处理批量检测，会话ID: {session_id[:8]}, 图片数量: {len(image_urls)}")
        
        if detection_types is None:
            detection_types = [0, 1]  # 默认检测所有类型
        
        # 下载并检测所有图片
        frame_detections = []
        debug_images = []
        inference_start = time.time()
        
        for i, url in enumerate(image_urls):
            frame = self.download_image(url)
            if frame is None:
                continue
            
            detections, original_name, result_name = self.detector.detect(frame, session_id)
            frame_detections.append(detections)
            
            if result_name:
                debug_images.append(f"http://your-domain.com/debug/{result_name}")
        
        inference_time = time.time() - inference_start
        
        # 分析检测结果
        results = {}
        
        # 手触螺丝检测
        if 0 in detection_types:
            hand_bolt_detected = False
            hand_bolt_confidence = 0.0
            hand_bolt_details = {}
            
            for frame_detection in frame_detections:
                detected, conf, details = self.check_hand_bolt_contact(
                    frame_detection.get('hands', []), 
                    frame_detection.get('bolts', [])
                )
                if detected and conf > hand_bolt_confidence:
                    hand_bolt_detected = True
                    hand_bolt_confidence = conf
                    hand_bolt_details = details
            
            results['hand_bolt_contact'] = {
                'detected': hand_bolt_detected,
                'confidence': hand_bolt_confidence,
                'details': hand_bolt_details
            }
        
        # 锤击螺丝检测
        if 1 in detection_types:
            hammer_detected, hammer_confidence, hammer_details = self.check_hammer_bolt_action(frame_detections)
            
            results['hammer_bolt_action'] = {
                'detected': hammer_detected,
                'confidence': hammer_confidence,
                'details': hammer_details
            }
        
        end_time = datetime.now()
        cost_time = (end_time - request_time).total_seconds()
        
        response = {
            "code": 200,
            "msg": "success",
            "cost_time": f"{cost_time:.3f}秒",
            "request_time": request_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
            "inference_time": f"{inference_time:.3f}秒",
            "debug_image_url_list": debug_images,
            "source_url": image_urls,
            "data": results
        }
        
        logger.info(f"批量检测完成，会话ID: {session_id[:8]}, 总耗时: {cost_time:.3f}秒")
        return response


# 图像保存目录配置
DEBUG_DIR = Path("debug_img")  # 原始图像保存目录
RESULT_DIR = Path("result_img")  # 检测结果图像保存目录
DEBUG_DIR.mkdir(exist_ok=True)
RESULT_DIR.mkdir(exist_ok=True)

# 自定义类别名称映射
CUSTOM_NAMES = {
    'hand': 'A_手',
    'bolt': 'B_螺丝',
    'hammer': 'C_锤子'
}
class ObjectDetector:
    """目标检测模块（适配YOLO11-obb模型，支持旋转边界框）"""
    
    def __init__(self, model_path='yolo11-obb.pt', conf_threshold=0.5):
        """
        初始化目标检测器
        
        Args:
            model_path: YOLO11-obb模型路径
            conf_threshold: 置信度阈值
        """
        logger.info(f"正在初始化目标检测器，模型路径: {model_path}")
        self.model = YOLO(model_path)
        self.conf_threshold = conf_threshold
        
        # 目标类别映射
        self.target_classes = {name: idx for idx, name in enumerate(CUSTOM_NAMES.keys())}
        self.class_names = CUSTOM_NAMES
        
        logger.info(f"目标类别映射: {self.target_classes}")
        logger.info("目标检测器初始化完成")
    
    def _generate_filenames(self, request_time: datetime, session_id: str) -> Dict[str, str]:
        """生成规范的文件名（原始图像和结果图像）"""
        timestamp = request_time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
        base_name = f"{session_id[:8]}_{timestamp}"
        
        return {
            "original": DEBUG_DIR / f"original_{base_name}.jpg",
            "result": RESULT_DIR / f"result_{base_name}.jpg",
            "original_name": f"original_{base_name}.jpg",
            "result_name": f"result_{base_name}.jpg"
        }
    
    def draw_detections(self, frame, results, filenames):
        """绘制检测结果并保存图像"""
        try:
            # 绘制检测结果
            plotted_frame = results[0].plot() if results else frame.copy()
            # 确保数组可写
            if not plotted_frame.flags.writeable:
                plotted_frame = plotted_frame.copy()
            
            # 添加统计信息
            height, width = plotted_frame.shape[:2]
            stats_text = f"Session: {filenames['original_name'].split('_')[1][:8]} | Time: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}"
            cv2.putText(plotted_frame, stats_text, (10, 30),
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 保存结果图像
            cv2.imwrite(str(filenames["result"]), plotted_frame)
            logger.info(f"结果图像已保存: {filenames['result']}")
            return filenames["result_name"]
        except Exception as e:
            logger.error(f"绘制检测结果失败: {str(e)}")
            return None
    
    def detect(self, frame, session_id=None):
        """
        检测图像中的目标对象（支持旋转边界框）
        
        Args:
            frame: 输入图像帧 (BGR格式)
            session_id: 会话ID（用于调试图像命名）
            
        Returns:
            tuple: (检测结果字典, 原始图像文件名, 结果图像文件名)
        """
        logger.info(f"开始目标检测，图像尺寸: {frame.shape}")
        request_time = datetime.now()
        
        # 生成文件名
        if not session_id:
            session_id = str(uuid.uuid4())
        filenames = self._generate_filenames(request_time, session_id)
        
        # 保存原始图像
        try:
            cv2.imwrite(str(filenames["original"]), frame)
            logger.info(f"原始图像已保存: {filenames['original']}")
        except Exception as e:
            logger.error(f"保存原始图像失败: {str(e)}")
        
        # 推理计时
        start_time = time.time()
        
        # 运行OBB推理
        try:
            results = self.model.predict(
                source=frame,
                conf=self.conf_threshold,
                imgsz=960,
                agnostic_nms=True,
                max_det=100,
                iou=0.45,
                save=False
            )
            inference_time = time.time() - start_time
            logger.info(f"推理完成，耗时: {inference_time:.3f}秒")
        except Exception as e:
            logger.error(f"模型推理失败: {str(e)}")
            return {}, None, None
        
        # 初始化检测结果
        detections = {
            'hands': [],
            'bolts': [],
            'hammers': []
        }
        detection_count = {'hands': 0, 'bolts': 0, 'hammers': 0}
        
        # 处理推理结果
        if results and len(results) > 0:
            result = results[0]
            
            # 处理旋转边界框（OBB）
            if hasattr(result, 'obb') and result.obb is not None:
                logger.info(f"检测到 {len(result.obb)} 个旋转框对象")
                
                for i in range(len(result.obb)):
                    try:
                        # 提取基本信息
                        class_id = int(result.obb.cls[i])
                        confidence = float(result.obb.conf[i])
                        class_name = result.names.get(class_id, f"class_{class_id}")
                        display_name = self.class_names.get(class_name, class_name)
                        
                        # 过滤低置信度和非目标类别
                        if confidence < self.conf_threshold:
                            continue
                        if class_name not in self.target_classes:
                            logger.debug(f"跳过非目标类别: {class_name}")
                            continue
                        
                        # 提取旋转框信息 (x, y, w, h, angle)
                        rbox = result.obb.xywhr[i].cpu().numpy()
                        x, y, w, h, angle = rbox
                        
                        # 计算外接矩形（用于兼容原有接触检测逻辑）
                        x1 = x - w/2
                        y1 = y - h/2
                        x2 = x + w/2
                        y2 = y + h/2
                        
                        # 构建检测信息
                        detection_info = {
                            'rbox': [float(x), float(y), float(w), float(h), float(angle)],  # 旋转框
                            'bbox': [float(x1), float(y1), float(x2), float(y2)],  # 外接矩形
                            'center': [float(x), float(y)],
                            'confidence': float(confidence),
                            'area': float(w * h),
                            'angle': float(angle),
                            'class_name': display_name
                        }
                        
                        # 根据类别分类存储
                        if class_name == 'hand':
                            detections['hands'].append(detection_info)
                            detection_count['hands'] += 1
                        elif class_name == 'bolt':
                            detections['bolts'].append(detection_info)
                            detection_count['bolts'] += 1
                        elif class_name == 'hammer':
                            detections['hammers'].append(detection_info)
                            detection_count['hammers'] += 1
                            
                    except Exception as e:
                        logger.error(f"处理第{i}个检测结果失败: {str(e)}")
                        continue
        
        logger.info(f"检测完成 - 手: {detection_count['hands']}, 螺丝: {detection_count['bolts']}, 锤子: {detection_count['hammers']}")
        
        # 绘制调试图像
        result_filename = self.draw_detections(frame, results, filenames)
        
        return detections, filenames["original_name"], result_filename

# 全局服务实例
detection_service = RealTimeDetectionService()

@app.route('/detect', methods=['POST'])
def detect_batch():
    """批量检测接口"""
    try:
        data = request.get_json()
        
        if not data or 'image_urls' not in data:
            return jsonify({
                "code": 500,
                "msg": "缺少必要参数 image_urls",
                "cost_time": "0.000秒",
                "request_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "data": {}
            }), 400
        
        image_urls = data['image_urls']
        detection_types = data.get('detection_types', [0, 1])  # 0:手触螺丝, 1:锤击螺丝
        
        if not isinstance(image_urls, list) or len(image_urls) == 0:
            return jsonify({
                "code": 500,
                "msg": "image_urls 必须是非空列表",
                "cost_time": "0.000秒",
                "request_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                "data": {}
            }), 400
        
        # 处理检测
        result = detection_service.process_batch(image_urls, detection_types)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"检测服务异常: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"检测服务异常: {str(e)}",
            "cost_time": "0.000秒",
            "request_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
            "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
            "data": {}
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)