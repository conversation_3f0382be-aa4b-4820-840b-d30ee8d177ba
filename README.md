# CLJX Detection Service

基于YOLO11-OBB的实时目标检测和行为分析服务，专门用于检测手、螺丝、锤子等物体，并分析它们之间的交互行为。

## 功能特性

- 🎯 **高精度检测**: 基于YOLO11-OBB模型，支持旋转边界框检测
- 🔄 **行为分析**: 智能分析手触螺丝、锤击螺丝等交互行为
- 🚀 **高性能**: 优化的推理流程，支持批量处理
- 📊 **性能监控**: 实时监控系统性能和资源使用
- 🛡️ **错误处理**: 完善的异常处理和日志记录
- 🔧 **易于配置**: 支持环境变量配置，灵活部署
- 📝 **完整文档**: 详细的API文档和使用指南

## 检测类型

- **类型0**: 手触螺丝检测 - 检测手部与螺丝的接触行为
- **类型1**: 锤击螺丝检测 - 检测锤子对螺丝的敲击行为

## 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 8GB+ RAM
- 2GB+ 可用磁盘空间

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd cljx_ls_check

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 启动服务

```bash
# 启动服务
python app.py

# 或使用环境变量
DEBUG_MODE=true PORT=8000 python app.py
```

服务将在 `http://localhost:5000` 启动。

## API 使用

### 健康检查

```bash
curl http://localhost:5000/health
```

### 批量检测

```bash
curl -X POST http://localhost:5000/detect \
  -H "Content-Type: application/json" \
  -d '{
    "image_urls": [
      "http://example.com/image1.jpg",
      "http://example.com/image2.jpg"
    ],
    "detection_types": [0, 1]
  }'
```

### 单张图片检测

```bash
curl -X POST http://localhost:5000/detect/single \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://example.com/image.jpg",
    "detection_types": [0, 1]
  }'
```

### API信息

```bash
curl http://localhost:5000/api/info
```

## 项目结构

```
cljx_ls_check/
├── api/                    # API路由模块
│   ├── __init__.py
│   └── routes.py
├── models/                 # 模型模块
│   ├── __init__.py
│   ├── object_detector.py  # 目标检测器
│   └── behavior_analyzer.py # 行为分析器
├── services/               # 服务模块
│   ├── __init__.py
│   └── detection_service.py # 检测服务
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── logger.py          # 日志工具
│   ├── image_utils.py     # 图像工具
│   ├── exceptions.py      # 异常定义
│   ├── decorators.py      # 装饰器
│   ├── performance.py     # 性能监控
│   └── cache.py           # 缓存工具
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_image_utils.py
│   └── test_behavior_analyzer.py
├── scripts/                # 脚本工具
│   └── code_quality.py    # 代码质量检查
├── logs/                   # 日志目录
├── debug_images/           # 调试图像目录
├── result_images/          # 结果图像目录
├── app.py                  # 主应用入口
├── config.py               # 配置文件
├── requirements.txt        # 依赖列表
├── .env.example           # 环境配置示例
├── pyproject.toml         # 项目配置
└── README.md              # 项目文档
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DEBUG_MODE` | `False` | 调试模式 |
| `HOST` | `0.0.0.0` | 服务主机 |
| `PORT` | `5000` | 服务端口 |
| `MODEL_PATH` | `yolo11-obb.pt` | 模型文件路径 |
| `CONFIDENCE_THRESHOLD` | `0.5` | 置信度阈值 |
| `HAND_BOLT_THRESHOLD` | `50` | 手触螺丝距离阈值 |
| `HAMMER_BOLT_THRESHOLD` | `80` | 锤击螺丝距离阈值 |
| `MAX_IMAGE_URLS` | `10` | 批量处理最大图片数 |

### 检测参数

可以通过环境变量或直接修改 `config.py` 来调整检测参数：

- `CONFIDENCE_THRESHOLD`: 检测置信度阈值
- `HAND_BOLT_CONTACT_THRESHOLD`: 手触螺丝判定距离
- `HAMMER_BOLT_CLOSE_THRESHOLD`: 锤击螺丝判定距离
- `INFERENCE_IMAGE_SIZE`: 推理图像尺寸

## 开发指南

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_image_utils.py -v

# 运行测试并生成覆盖率报告
python -m pytest tests/ --cov=. --cov-report=html
```

### 代码质量检查

```bash
# 运行代码质量检查
python scripts/code_quality.py

# 单独运行各种检查
python -m flake8 .
python -m mypy .
python -m bandit -r .
```

### 性能监控

服务内置性能监控功能，可以通过以下方式查看：

```python
from utils.performance import performance_monitor

# 获取性能统计
stats = performance_monitor.get_statistics()
print(stats)
```

## 部署指南

### Docker 部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "app.py"]
```

### 生产环境配置

1. 使用 Gunicorn 作为 WSGI 服务器
2. 配置 Nginx 作为反向代理
3. 设置适当的环境变量
4. 配置日志轮转
5. 设置监控和告警

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认模型文件完整性
   - 检查CUDA环境配置

2. **内存不足**
   - 减少批量处理图片数量
   - 降低推理图像尺寸
   - 启用图像缓存

3. **推理速度慢**
   - 启用GPU加速
   - 使用半精度推理
   - 优化图像预处理

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 运行测试
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: CLJX Team
- 邮箱: <EMAIL>
- 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)
