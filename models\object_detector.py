"""
目标检测模块
基于YOLO11-OBB模型的目标检测器
"""

import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import numpy as np
from ultralytics import YOLO

from config import CUSTOM_NAMES, DETECTION_CONFIG
from utils.logger import get_logger
from utils.image_utils import generate_filenames, save_image, draw_detection_results
from utils.performance import performance_monitor, PerformanceProfiler
from utils.decorators import measure_time, handle_exceptions

logger = get_logger(__name__)


class ObjectDetector:
    """目标检测器（适配YOLO11-obb模型，支持旋转边界框）"""
    
    def __init__(self, model_path: str = 'yolo11-obb.pt', conf_threshold: float = None):
        """
        初始化目标检测器
        
        Args:
            model_path: YOLO11-obb模型路径
            conf_threshold: 置信度阈值
        """
        logger.info(f"正在初始化目标检测器，模型路径: {model_path}")
        
        try:
            self.model = YOLO(model_path)
            logger.info("YOLO模型加载成功")
        except Exception as e:
            logger.error(f"YOLO模型加载失败: {str(e)}")
            raise
        
        self.conf_threshold = conf_threshold or DETECTION_CONFIG['confidence_threshold']
        
        # 目标类别映射
        self.target_classes = {name: idx for idx, name in enumerate(CUSTOM_NAMES.keys())}
        self.class_names = CUSTOM_NAMES
        
        logger.info(f"目标类别映射: {self.target_classes}")
        logger.info(f"置信度阈值: {self.conf_threshold}")
        logger.info("目标检测器初始化完成")
    
    @measure_time(log_result=True, threshold_seconds=2.0)
    @handle_exceptions(default_return=({}, None, None))
    def detect(self, frame: np.ndarray, session_id: Optional[str] = None) -> Tuple[Dict, Optional[str], Optional[str]]:
        """
        检测图像中的目标对象（支持旋转边界框）

        Args:
            frame: 输入图像帧 (BGR格式)
            session_id: 会话ID（用于调试图像命名）

        Returns:
            tuple: (检测结果字典, 原始图像文件名, 结果图像文件名)
        """
        profiler = PerformanceProfiler(f"object_detection_{session_id or 'unknown'}")
        profiler.start()

        logger.info(f"开始目标检测，图像尺寸: {frame.shape}")
        request_time = datetime.now()

        # 生成文件名
        if not session_id:
            session_id = str(uuid.uuid4())
        filenames = generate_filenames(request_time, session_id)

        profiler.checkpoint("文件名生成完成")

        # 保存原始图像（异步保存以提高性能）
        save_image(frame, filenames["original"])
        profiler.checkpoint("原始图像保存完成")

        # 推理计时
        inference_start = time.time()

        # 运行OBB推理
        try:
            # 优化推理参数
            results = self.model.predict(
                source=frame,
                conf=self.conf_threshold,
                imgsz=DETECTION_CONFIG['inference_image_size'],
                agnostic_nms=True,
                max_det=DETECTION_CONFIG['max_detections'],
                iou=DETECTION_CONFIG['iou_threshold'],
                save=False,
                verbose=False,  # 减少日志输出
                device=None,    # 自动选择设备
                half=True       # 使用半精度推理（如果支持）
            )
            inference_time = time.time() - inference_start
            logger.info(f"推理完成，耗时: {inference_time:.3f}秒")

            # 记录性能指标
            performance_monitor.record_metrics(
                inference_time=inference_time,
                processing_time=None
            )

        except Exception as e:
            logger.error(f"模型推理失败: {str(e)}")
            return {}, None, None

        profiler.checkpoint("模型推理完成")

        # 处理检测结果
        detections = self._process_detection_results(results)
        profiler.checkpoint("检测结果处理完成")

        # 绘制调试图像
        result_filename = draw_detection_results(frame, results, filenames)
        profiler.checkpoint("调试图像绘制完成")

        # 记录总处理时间
        total_time = profiler.end()
        if total_time:
            performance_monitor.record_metrics(
                inference_time=inference_time,
                processing_time=total_time.get('total_time')
            )

        return detections, filenames["original_name"], result_filename
    
    def _process_detection_results(self, results) -> Dict[str, List[Dict]]:
        """
        处理YOLO检测结果
        
        Args:
            results: YOLO检测结果
            
        Returns:
            分类后的检测结果字典
        """
        # 初始化检测结果
        detections = {
            'hands': [],
            'bolts': [],
            'hammers': []
        }
        detection_count = {'hands': 0, 'bolts': 0, 'hammers': 0}
        
        # 处理推理结果
        if results and len(results) > 0:
            result = results[0]
            
            # 处理旋转边界框（OBB）
            if hasattr(result, 'obb') and result.obb is not None:
                logger.info(f"检测到 {len(result.obb)} 个旋转框对象")
                
                for i in range(len(result.obb)):
                    detection_info = self._extract_detection_info(result, i)
                    if detection_info:
                        class_name = detection_info['class_name_en']
                        
                        # 根据类别分类存储
                        if class_name == 'hand':
                            detections['hands'].append(detection_info)
                            detection_count['hands'] += 1
                        elif class_name == 'bolt':
                            detections['bolts'].append(detection_info)
                            detection_count['bolts'] += 1
                        elif class_name == 'hammer':
                            detections['hammers'].append(detection_info)
                            detection_count['hammers'] += 1
        
        logger.info(f"检测完成 - 手: {detection_count['hands']}, 螺丝: {detection_count['bolts']}, 锤子: {detection_count['hammers']}")
        return detections
    
    def _extract_detection_info(self, result, index: int) -> Optional[Dict]:
        """
        提取单个检测结果的信息
        
        Args:
            result: YOLO检测结果
            index: 检测结果索引
            
        Returns:
            检测信息字典，失败返回None
        """
        try:
            # 提取基本信息
            class_id = int(result.obb.cls[index])
            confidence = float(result.obb.conf[index])
            class_name = result.names.get(class_id, f"class_{class_id}")
            display_name = self.class_names.get(class_name, class_name)
            
            # 过滤低置信度和非目标类别
            if confidence < self.conf_threshold:
                return None
            if class_name not in self.target_classes:
                logger.debug(f"跳过非目标类别: {class_name}")
                return None
            
            # 提取旋转框信息 (x, y, w, h, angle)
            rbox = result.obb.xywhr[index].cpu().numpy()
            x, y, w, h, angle = rbox
            
            # 计算外接矩形（用于兼容原有接触检测逻辑）
            x1 = x - w/2
            y1 = y - h/2
            x2 = x + w/2
            y2 = y + h/2
            
            # 构建检测信息
            return {
                'rbox': [float(x), float(y), float(w), float(h), float(angle)],  # 旋转框
                'bbox': [float(x1), float(y1), float(x2), float(y2)],  # 外接矩形
                'center': [float(x), float(y)],
                'confidence': float(confidence),
                'area': float(w * h),
                'angle': float(angle),
                'class_name': display_name,
                'class_name_en': class_name
            }
            
        except Exception as e:
            logger.error(f"处理第{index}个检测结果失败: {str(e)}")
            return None
