# API 文档

CLJX Detection Service REST API 详细文档。

## 基础信息

- **基础URL**: `http://localhost:5000`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 认证

当前版本不需要认证，后续版本可能会添加API密钥认证。

## 响应格式

所有API响应都遵循统一的JSON格式：

```json
{
  "code": 200,
  "msg": "success",
  "cost_time": "1.234秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:46.357",
  "data": {}
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `code` | integer | 状态码，200表示成功，其他表示错误 |
| `msg` | string | 响应消息 |
| `cost_time` | string | 总耗时 |
| `request_time` | string | 请求开始时间 |
| `end_time` | string | 请求结束时间 |
| `data` | object | 响应数据 |

## API 端点

### 1. 健康检查

检查服务状态和系统信息。

**请求**
```
GET /health
```

**响应示例**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "gpu_available": true,
  "gpu_count": 1,
  "timestamp": "2023-12-01 10:30:45"
}
```

### 2. 批量图片检测

对多张图片进行批量检测和行为分析。

**请求**
```
POST /detect
Content-Type: application/json

{
  "image_urls": [
    "http://example.com/image1.jpg",
    "http://example.com/image2.jpg"
  ],
  "detection_types": [0, 1]
}
```

**请求参数**

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `image_urls` | array | 是 | 图片URL列表，最多10张 |
| `detection_types` | array | 否 | 检测类型列表，默认[0,1] |

**检测类型说明**
- `0`: 手触螺丝检测
- `1`: 锤击螺丝检测

**响应示例**
```json
{
  "code": 200,
  "msg": "success",
  "cost_time": "2.345秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:47.468",
  "inference_time": "1.234秒",
  "debug_image_url_list": [
    "http://your-domain.com/debug/result_12345678_20231201_103045_123.jpg"
  ],
  "source_url": [
    "http://example.com/image1.jpg",
    "http://example.com/image2.jpg"
  ],
  "processed_images": 2,
  "data": {
    "hand_bolt_contact": {
      "detected": true,
      "confidence": 0.85,
      "details": {
        "hand_center": [150.5, 200.3],
        "bolt_center": [155.2, 205.1],
        "distance": 6.8,
        "hand_conf": 0.9,
        "bolt_conf": 0.8,
        "hand_class": "A_手",
        "bolt_class": "B_螺丝"
      }
    },
    "hammer_bolt_action": {
      "detected": false,
      "confidence": 0.0,
      "details": {}
    }
  }
}
```

### 3. 单张图片检测

对单张图片进行检测和行为分析。

**请求**
```
POST /detect/single
Content-Type: application/json

{
  "image_url": "http://example.com/image.jpg",
  "detection_types": [0, 1]
}
```

**请求参数**

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `image_url` | string | 是 | 图片URL |
| `detection_types` | array | 否 | 检测类型列表，默认[0,1] |

**响应格式与批量检测相同**

### 4. API信息

获取API基本信息和配置。

**请求**
```
GET /api/info
```

**响应示例**
```json
{
  "name": "CLJX Detection Service",
  "version": "1.0.0",
  "description": "基于YOLO11-OBB的实时目标检测和行为分析服务",
  "endpoints": {
    "/health": "健康检查",
    "/detect": "批量图片检测",
    "/detect/single": "单张图片检测",
    "/api/info": "API信息"
  },
  "detection_types": {
    "0": "手触螺丝检测",
    "1": "锤击螺丝检测"
  },
  "supported_formats": ["jpg", "jpeg", "png", "bmp"],
  "max_images_per_batch": 10,
  "request_timeout": "30秒"
}
```

## 错误处理

### 错误响应格式

```json
{
  "code": 400,
  "msg": "错误描述",
  "cost_time": "0.001秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:45.124",
  "data": {}
}
```

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和内容 |
| 404 | 接口不存在 | 检查请求URL |
| 405 | 请求方法不允许 | 使用正确的HTTP方法 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 具体错误示例

**参数缺失**
```json
{
  "code": 500,
  "msg": "缺少必要参数 image_urls",
  "cost_time": "0.001秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:45.124",
  "data": {}
}
```

**URL格式错误**
```json
{
  "code": 500,
  "msg": "第 1 个URL格式无效，必须以http://或https://开头",
  "cost_time": "0.001秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:45.124",
  "data": {}
}
```

**图片数量超限**
```json
{
  "code": 500,
  "msg": "图片数量不能超过 10 张",
  "cost_time": "0.001秒",
  "request_time": "2023-12-01 10:30:45.123",
  "end_time": "2023-12-01 10:30:45.124",
  "data": {}
}
```

## 使用限制

### 请求限制

- 单次批量检测最多10张图片
- 图片URL必须可公开访问
- 支持的图片格式：JPG, JPEG, PNG, BMP
- 建议图片大小不超过10MB
- 请求超时时间：30秒

### 性能建议

- 批量处理比单张处理更高效
- 图片尺寸建议在1920x1080以内
- 避免频繁的小批量请求
- 使用CDN加速图片访问

## 示例代码

### Python 示例

```python
import requests
import json

# 批量检测示例
def batch_detect(image_urls, detection_types=[0, 1]):
    url = "http://localhost:5000/detect"
    data = {
        "image_urls": image_urls,
        "detection_types": detection_types
    }
    
    response = requests.post(url, json=data, timeout=60)
    return response.json()

# 使用示例
result = batch_detect([
    "http://example.com/image1.jpg",
    "http://example.com/image2.jpg"
])

print(json.dumps(result, indent=2, ensure_ascii=False))
```

### JavaScript 示例

```javascript
// 单张图片检测示例
async function singleDetect(imageUrl, detectionTypes = [0, 1]) {
    const url = 'http://localhost:5000/detect/single';
    const data = {
        image_url: imageUrl,
        detection_types: detectionTypes
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        return await response.json();
    } catch (error) {
        console.error('Detection failed:', error);
        throw error;
    }
}

// 使用示例
singleDetect('http://example.com/image.jpg')
    .then(result => console.log(result))
    .catch(error => console.error(error));
```

### cURL 示例

```bash
# 健康检查
curl -X GET http://localhost:5000/health

# 批量检测
curl -X POST http://localhost:5000/detect \
  -H "Content-Type: application/json" \
  -d '{
    "image_urls": [
      "http://example.com/image1.jpg",
      "http://example.com/image2.jpg"
    ],
    "detection_types": [0, 1]
  }'

# 单张检测
curl -X POST http://localhost:5000/detect/single \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "http://example.com/image.jpg",
    "detection_types": [0]
  }'
```

## 版本历史

### v1.0.0 (2023-12-01)
- 初始版本发布
- 支持手触螺丝和锤击螺丝检测
- 批量和单张图片处理
- 基础性能监控

## 技术支持

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- GitHub Issues: [项目地址](https://github.com/your-repo/issues)
