"""
行为分析器测试
"""

import unittest
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent))

from models.behavior_analyzer import BehaviorAnalyzer


class TestBehaviorAnalyzer(unittest.TestCase):
    """行为分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = BehaviorAnalyzer()
    
    def test_check_hand_bolt_contact_no_objects(self):
        """测试无物体时的手触螺丝检测"""
        # 空列表
        detected, confidence, details = self.analyzer.check_hand_bolt_contact([], [])
        self.assertFalse(detected)
        self.assertEqual(confidence, 0.0)
        self.assertEqual(details, {})
        
        # 只有手，没有螺丝
        hands = [{'center': [100, 100], 'confidence': 0.8}]
        detected, confidence, details = self.analyzer.check_hand_bolt_contact(hands, [])
        self.assertFalse(detected)
        self.assertEqual(confidence, 0.0)
        self.assertEqual(details, {})
    
    def test_check_hand_bolt_contact_close_distance(self):
        """测试近距离手触螺丝检测"""
        hands = [{'center': [100, 100], 'confidence': 0.8, 'class_name': '手'}]
        bolts = [{'center': [110, 110], 'confidence': 0.9, 'class_name': '螺丝'}]
        
        detected, confidence, details = self.analyzer.check_hand_bolt_contact(hands, bolts)
        
        self.assertTrue(detected)
        self.assertAlmostEqual(confidence, 0.8, places=2)  # min(0.8, 0.9) = 0.8
        self.assertIn('hand_center', details)
        self.assertIn('bolt_center', details)
        self.assertIn('distance', details)
    
    def test_check_hand_bolt_contact_far_distance(self):
        """测试远距离手触螺丝检测"""
        hands = [{'center': [100, 100], 'confidence': 0.8, 'class_name': '手'}]
        bolts = [{'center': [200, 200], 'confidence': 0.9, 'class_name': '螺丝'}]  # 距离约141像素
        
        detected, confidence, details = self.analyzer.check_hand_bolt_contact(hands, bolts)
        
        self.assertFalse(detected)
        self.assertEqual(confidence, 0.0)
        self.assertEqual(details, {})
    
    def test_check_hand_bolt_contact_multiple_objects(self):
        """测试多个物体的手触螺丝检测"""
        hands = [
            {'center': [100, 100], 'confidence': 0.8, 'class_name': '手'},
            {'center': [300, 300], 'confidence': 0.7, 'class_name': '手'}
        ]
        bolts = [
            {'center': [110, 110], 'confidence': 0.9, 'class_name': '螺丝'},  # 接近第一只手
            {'center': [400, 400], 'confidence': 0.6, 'class_name': '螺丝'}   # 远离所有手
        ]
        
        detected, confidence, details = self.analyzer.check_hand_bolt_contact(hands, bolts)
        
        self.assertTrue(detected)
        self.assertAlmostEqual(confidence, 0.8, places=2)  # 最高置信度匹配
    
    def test_check_hammer_bolt_action_insufficient_frames(self):
        """测试帧数不足的锤击检测"""
        # 只有一帧
        frame_detections = [{'hammers': [], 'bolts': []}]
        
        detected, confidence, details = self.analyzer.check_hammer_bolt_action(frame_detections)
        
        self.assertFalse(detected)
        self.assertEqual(confidence, 0.0)
        self.assertEqual(details, {})
    
    def test_check_hammer_bolt_action_no_objects(self):
        """测试无物体的锤击检测"""
        frame_detections = [
            {'hammers': [], 'bolts': []},
            {'hammers': [], 'bolts': []}
        ]
        
        detected, confidence, details = self.analyzer.check_hammer_bolt_action(frame_detections)
        
        self.assertFalse(detected)
        self.assertEqual(confidence, 0.0)
        self.assertEqual(details, {})
    
    def test_check_hammer_bolt_action_close_distance(self):
        """测试近距离锤击检测"""
        frame_detections = [
            {
                'hammers': [{'center': [100, 100], 'confidence': 0.8, 'class_name': '锤子'}],
                'bolts': [{'center': [120, 120], 'confidence': 0.9, 'class_name': '螺丝'}]
            },
            {
                'hammers': [{'center': [105, 105], 'confidence': 0.8, 'class_name': '锤子'}],
                'bolts': [{'center': [120, 120], 'confidence': 0.9, 'class_name': '螺丝'}]
            }
        ]
        
        detected, confidence, details = self.analyzer.check_hammer_bolt_action(frame_detections)
        
        self.assertTrue(detected)
        self.assertGreater(confidence, 0.0)
        self.assertIn('hammer_center', details)
        self.assertIn('bolt_center', details)
    
    def test_check_hammer_bolt_action_moving_closer(self):
        """测试锤子向螺丝移动的检测"""
        frame_detections = [
            {
                'hammers': [{'center': [100, 100], 'confidence': 0.8, 'class_name': '锤子'}],
                'bolts': [{'center': [200, 200], 'confidence': 0.9, 'class_name': '螺丝'}]
            },
            {
                'hammers': [{'center': [150, 150], 'confidence': 0.8, 'class_name': '锤子'}],  # 向螺丝移动
                'bolts': [{'center': [200, 200], 'confidence': 0.9, 'class_name': '螺丝'}]
            }
        ]
        
        detected, confidence, details = self.analyzer.check_hammer_bolt_action(frame_detections)
        
        self.assertTrue(detected)
        self.assertGreater(confidence, 0.0)
        self.assertEqual(details['movement_direction'], 'closer')
    
    def test_analyze_batch_behaviors(self):
        """测试批量行为分析"""
        frame_detections = [
            {
                'hands': [{'center': [100, 100], 'confidence': 0.8, 'class_name': '手'}],
                'bolts': [{'center': [110, 110], 'confidence': 0.9, 'class_name': '螺丝'}],
                'hammers': []
            }
        ]
        
        # 只检测手触螺丝
        results = self.analyzer.analyze_batch_behaviors(frame_detections, [0])
        
        self.assertIn('hand_bolt_contact', results)
        self.assertNotIn('hammer_bolt_action', results)
        self.assertTrue(results['hand_bolt_contact']['detected'])
        
        # 检测所有类型
        results = self.analyzer.analyze_batch_behaviors(frame_detections, [0, 1])
        
        self.assertIn('hand_bolt_contact', results)
        self.assertIn('hammer_bolt_action', results)
    
    def test_find_matching_hammer(self):
        """测试锤子匹配算法"""
        current_hammer = {'center': [100, 100], 'confidence': 0.8}
        
        # 测试找到匹配的锤子
        next_hammers = [
            {'center': [105, 105], 'confidence': 0.8},  # 移动距离小，应该匹配
            {'center': [200, 200], 'confidence': 0.9}   # 移动距离大，不应该匹配
        ]
        
        matched_hammer = self.analyzer._find_matching_hammer(current_hammer, next_hammers)
        
        self.assertIsNotNone(matched_hammer)
        self.assertEqual(matched_hammer['center'], [105, 105])
        
        # 测试没有匹配的锤子
        far_hammers = [
            {'center': [300, 300], 'confidence': 0.8}  # 距离太远
        ]
        
        matched_hammer = self.analyzer._find_matching_hammer(current_hammer, far_hammers)
        self.assertIsNone(matched_hammer)


if __name__ == '__main__':
    unittest.main()
