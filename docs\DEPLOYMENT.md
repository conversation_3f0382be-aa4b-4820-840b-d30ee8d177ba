# 部署指南

本文档详细介绍了CLJX Detection Service的各种部署方式。

## 部署前准备

### 系统要求

**最低配置**
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 20GB 可用空间
- 操作系统: Ubuntu 18.04+ / CentOS 7+ / Windows 10+

**推荐配置**
- CPU: 8核心
- 内存: 16GB RAM
- GPU: NVIDIA GTX 1060 或更高
- 存储: 50GB SSD
- 网络: 100Mbps+

### 软件依赖

- Python 3.8+
- CUDA 11.0+ (GPU部署)
- Docker (容器部署)
- Nginx (生产环境)

## 开发环境部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd cljx_ls_check
```

### 2. 创建虚拟环境

```bash
# 使用 venv
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 使用 conda
conda create -n cljx python=3.9
conda activate cljx
```

### 3. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 4. 配置环境

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置
nano .env
```

### 5. 下载模型

```bash
# 下载YOLO11-OBB模型
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolo11-obb.pt
```

### 6. 启动服务

```bash
python app.py
```

## 生产环境部署

### 方式一：直接部署

#### 1. 系统配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装系统依赖
sudo apt install -y python3 python3-pip python3-venv nginx supervisor

# 创建服务用户
sudo useradd -m -s /bin/bash cljx
sudo usermod -aG sudo cljx
```

#### 2. 应用部署

```bash
# 切换到服务用户
sudo su - cljx

# 克隆项目
git clone <repository-url> /home/<USER>/cljx_detection
cd /home/<USER>/cljx_detection

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
nano .env
```

#### 3. Gunicorn 配置

创建 `gunicorn.conf.py`:

```python
# gunicorn.conf.py
bind = "127.0.0.1:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
user = "cljx"
group = "cljx"
tmp_upload_dir = None
errorlog = "/home/<USER>/cljx_detection/logs/gunicorn_error.log"
accesslog = "/home/<USER>/cljx_detection/logs/gunicorn_access.log"
loglevel = "info"
```

#### 4. Supervisor 配置

创建 `/etc/supervisor/conf.d/cljx_detection.conf`:

```ini
[program:cljx_detection]
command=/home/<USER>/cljx_detection/venv/bin/gunicorn -c gunicorn.conf.py app:app
directory=/home/<USER>/cljx_detection
user=cljx
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/cljx_detection/logs/supervisor.log
environment=PATH="/home/<USER>/cljx_detection/venv/bin"
```

启动服务:

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start cljx_detection
```

#### 5. Nginx 配置

创建 `/etc/nginx/sites-available/cljx_detection`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 50M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    location /debug/ {
        alias /home/<USER>/cljx_detection/result_images/;
        expires 1h;
        add_header Cache-Control "public, immutable";
    }
    
    location /health {
        proxy_pass http://127.0.0.1:5000/health;
        access_log off;
    }
}
```

启用站点:

```bash
sudo ln -s /etc/nginx/sites-available/cljx_detection /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 方式二：Docker 部署

#### 1. 创建 Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p logs debug_images result_images

# 设置环境变量
ENV PYTHONPATH=/app
ENV HOST=0.0.0.0
ENV PORT=5000

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 启动命令
CMD ["python", "app.py"]
```

#### 2. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  cljx-detection:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DEBUG_MODE=false
      - HOST=0.0.0.0
      - PORT=5000
      - MODEL_PATH=yolo11-obb.pt
    volumes:
      - ./logs:/app/logs
      - ./debug_images:/app/debug_images
      - ./result_images:/app/result_images
      - ./models:/app/models
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./result_images:/usr/share/nginx/html/debug:ro
    depends_on:
      - cljx-detection
    restart: unless-stopped
```

#### 3. 构建和启动

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式三：Kubernetes 部署

#### 1. 创建 Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cljx-detection
  labels:
    app: cljx-detection
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cljx-detection
  template:
    metadata:
      labels:
        app: cljx-detection
    spec:
      containers:
      - name: cljx-detection
        image: cljx/detection-service:latest
        ports:
        - containerPort: 5000
        env:
        - name: DEBUG_MODE
          value: "false"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "5000"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: model-storage
          mountPath: /app/models
        - name: logs-storage
          mountPath: /app/logs
      volumes:
      - name: model-storage
        persistentVolumeClaim:
          claimName: model-pvc
      - name: logs-storage
        persistentVolumeClaim:
          claimName: logs-pvc
```

#### 2. 创建 Service

```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: cljx-detection-service
spec:
  selector:
    app: cljx-detection
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: LoadBalancer
```

#### 3. 部署到集群

```bash
# 应用配置
kubectl apply -f k8s/

# 查看状态
kubectl get pods -l app=cljx-detection
kubectl get services

# 查看日志
kubectl logs -l app=cljx-detection -f
```

## 性能优化

### 1. GPU 加速

```bash
# 安装CUDA驱动
sudo apt install nvidia-driver-470

# 安装CUDA toolkit
wget https://developer.download.nvidia.com/compute/cuda/11.8.0/local_installers/cuda_11.8.0_520.61.05_linux.run
sudo sh cuda_11.8.0_520.61.05_linux.run

# 验证安装
nvidia-smi
nvcc --version
```

### 2. 内存优化

```python
# 在 .env 中配置
INFERENCE_IMAGE_SIZE=640  # 降低推理尺寸
MAX_DETECTIONS=50         # 减少最大检测数
CONFIDENCE_THRESHOLD=0.6  # 提高置信度阈值
```

### 3. 并发优化

```python
# gunicorn.conf.py
workers = min(4, (cpu_count() * 2) + 1)
worker_class = "gevent"
worker_connections = 1000
```

## 监控和日志

### 1. 日志配置

```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/cljx_detection

/home/<USER>/cljx_detection/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 cljx cljx
    postrotate
        supervisorctl restart cljx_detection
    endscript
}
```

### 2. 监控配置

使用 Prometheus + Grafana:

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'cljx-detection'
    static_configs:
      - targets: ['localhost:5000']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查日志
   sudo supervisorctl tail -f cljx_detection stderr
   
   # 检查端口占用
   sudo netstat -tlnp | grep :5000
   ```

2. **内存不足**
   ```bash
   # 监控内存使用
   free -h
   htop
   
   # 调整worker数量
   nano gunicorn.conf.py
   ```

3. **GPU不可用**
   ```bash
   # 检查GPU状态
   nvidia-smi
   
   # 检查CUDA版本
   nvcc --version
   python -c "import torch; print(torch.cuda.is_available())"
   ```

### 性能调优

1. **推理优化**
   - 使用TensorRT加速
   - 启用混合精度
   - 批量处理

2. **网络优化**
   - 启用gzip压缩
   - 配置CDN
   - 使用连接池

3. **存储优化**
   - 使用SSD存储
   - 配置缓存
   - 定期清理临时文件

## 安全配置

### 1. 防火墙配置

```bash
# 配置ufw
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL证书

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 访问控制

```nginx
# 限制访问频率
location /detect {
    limit_req zone=api burst=10 nodelay;
    proxy_pass http://127.0.0.1:5000;
}
```

## 备份和恢复

### 1. 数据备份

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/cljx_detection_$DATE"

mkdir -p $BACKUP_DIR
cp -r /home/<USER>/cljx_detection/logs $BACKUP_DIR/
cp -r /home/<USER>/cljx_detection/models $BACKUP_DIR/
cp /home/<USER>/cljx_detection/.env $BACKUP_DIR/

tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### 2. 自动备份

```bash
# 添加到crontab
0 2 * * * /home/<USER>/backup.sh
```

## 联系支持

如需技术支持，请联系：
- 邮箱: <EMAIL>
- 电话: +86-xxx-xxxx-xxxx
- 在线支持: https://support.cljx.com
