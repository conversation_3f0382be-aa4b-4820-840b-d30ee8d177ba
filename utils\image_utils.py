"""
图像处理工具模块
提供图像下载、保存、处理等功能
"""

import cv2
import numpy as np
import requests
import uuid
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Tuple

from config import DEBUG_DIR, RESULT_DIR, DETECTION_CONFIG
from utils.logger import get_logger

logger = get_logger(__name__)


def download_image(url: str, timeout: int = None) -> Optional[np.ndarray]:
    """
    下载在线图片
    
    Args:
        url: 图片URL
        timeout: 下载超时时间（秒）
    
    Returns:
        图片数组，失败返回None
    """
    if timeout is None:
        timeout = DETECTION_CONFIG['download_timeout']
    
    try:
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        
        # 转换为OpenCV格式
        img_array = np.frombuffer(response.content, np.uint8)
        frame = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        if frame is None:
            logger.error(f"无法解码图片: {url}")
            return None
            
        logger.info(f"成功下载图片: {url}, 尺寸: {frame.shape}")
        return frame
        
    except requests.exceptions.Timeout:
        logger.error(f"下载图片超时: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"下载图片失败 {url}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"处理图片时发生未知错误 {url}: {str(e)}")
        return None


def generate_filenames(request_time: datetime, session_id: str) -> Dict[str, str]:
    """
    生成规范的文件名（原始图像和结果图像）
    
    Args:
        request_time: 请求时间
        session_id: 会话ID
    
    Returns:
        包含文件路径和文件名的字典
    """
    timestamp = request_time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    base_name = f"{session_id[:8]}_{timestamp}"
    
    return {
        "original": DEBUG_DIR / f"original_{base_name}.jpg",
        "result": RESULT_DIR / f"result_{base_name}.jpg",
        "original_name": f"original_{base_name}.jpg",
        "result_name": f"result_{base_name}.jpg"
    }


def save_image(image: np.ndarray, filepath: Path) -> bool:
    """
    保存图像到指定路径
    
    Args:
        image: 图像数组
        filepath: 保存路径
    
    Returns:
        保存是否成功
    """
    try:
        # 确保目录存在
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存图像
        success = cv2.imwrite(str(filepath), image)
        if success:
            logger.info(f"图像已保存: {filepath}")
            return True
        else:
            logger.error(f"保存图像失败: {filepath}")
            return False
            
    except Exception as e:
        logger.error(f"保存图像时发生错误 {filepath}: {str(e)}")
        return False


def draw_detection_results(
    frame: np.ndarray, 
    results, 
    filenames: Dict[str, str]
) -> Optional[str]:
    """
    绘制检测结果并保存图像
    
    Args:
        frame: 原始图像
        results: YOLO检测结果
        filenames: 文件名字典
    
    Returns:
        结果图像文件名，失败返回None
    """
    try:
        # 绘制检测结果
        plotted_frame = results[0].plot() if results else frame.copy()
        
        # 确保数组可写
        if not plotted_frame.flags.writeable:
            plotted_frame = plotted_frame.copy()
        
        # 添加统计信息
        height, width = plotted_frame.shape[:2]
        stats_text = f"Session: {filenames['original_name'].split('_')[1][:8]} | Time: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}"
        cv2.putText(
            plotted_frame, 
            stats_text, 
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX, 
            0.7, 
            (255, 255, 255), 
            2
        )
        
        # 保存结果图像
        if save_image(plotted_frame, Path(filenames["result"])):
            return filenames["result_name"]
        else:
            return None
            
    except Exception as e:
        logger.error(f"绘制检测结果失败: {str(e)}")
        return None


def calculate_distance(center1: Tuple[float, float], center2: Tuple[float, float]) -> float:
    """
    计算两个中心点之间的欧几里得距离
    
    Args:
        center1: 第一个点的坐标 (x, y)
        center2: 第二个点的坐标 (x, y)
    
    Returns:
        两点之间的距离
    """
    return np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)


def validate_image_urls(image_urls) -> Tuple[bool, str]:
    """
    验证图片URL列表
    
    Args:
        image_urls: 图片URL列表
    
    Returns:
        (是否有效, 错误信息)
    """
    if not isinstance(image_urls, list):
        return False, "image_urls 必须是列表类型"
    
    if len(image_urls) == 0:
        return False, "image_urls 不能为空"
    
    from config import API_CONFIG
    max_urls = API_CONFIG['max_image_urls']
    if len(image_urls) > max_urls:
        return False, f"图片数量不能超过 {max_urls} 张"
    
    for i, url in enumerate(image_urls):
        if not isinstance(url, str):
            return False, f"第 {i+1} 个URL必须是字符串类型"
        
        if not url.strip():
            return False, f"第 {i+1} 个URL不能为空"
        
        # 简单的URL格式验证
        if not (url.startswith('http://') or url.startswith('https://')):
            return False, f"第 {i+1} 个URL格式无效，必须以http://或https://开头"
    
    return True, ""
