# CLJX Detection Service Makefile

.PHONY: help install install-dev test lint format clean run docker-build docker-run docs

# 默认目标
help:
	@echo "CLJX Detection Service - 可用命令:"
	@echo ""
	@echo "  install      - 安装生产依赖"
	@echo "  install-dev  - 安装开发依赖"
	@echo "  test         - 运行测试"
	@echo "  test-cov     - 运行测试并生成覆盖率报告"
	@echo "  lint         - 运行代码质量检查"
	@echo "  format       - 格式化代码"
	@echo "  clean        - 清理临时文件"
	@echo "  run          - 启动开发服务器"
	@echo "  run-prod     - 启动生产服务器"
	@echo "  docker-build - 构建Docker镜像"
	@echo "  docker-run   - 运行Docker容器"
	@echo "  docs         - 生成文档"
	@echo "  quality      - 运行完整的代码质量检查"
	@echo ""

# 安装依赖
install:
	pip install -r requirements.txt

install-dev: install
	pip install -r requirements-dev.txt

# 测试
test:
	python -m pytest tests/ -v

test-cov:
	python -m pytest tests/ --cov=. --cov-report=html --cov-report=term

test-watch:
	python -m pytest tests/ -v --tb=short -x --looponfail

# 代码质量检查
lint:
	python -m flake8 .
	python -m mypy .
	python -m bandit -r . -x tests/,venv/,.venv/

lint-fix:
	python -m autopep8 --in-place --recursive .

# 代码格式化
format:
	python -m black .
	python -m isort .

format-check:
	python -m black --check .
	python -m isort --check-only .

# 安全检查
security:
	python -m bandit -r .
	python -m safety check

# 完整的代码质量检查
quality:
	python scripts/code_quality.py

# 清理
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf dist/
	rm -rf build/

clean-logs:
	rm -rf logs/*.log
	rm -rf debug_images/*
	rm -rf result_images/*

# 运行服务
run:
	python app.py

run-dev:
	DEBUG_MODE=true python app.py

run-prod:
	gunicorn -c gunicorn.conf.py app:app

# Docker
docker-build:
	docker build -t cljx/detection-service:latest .

docker-run:
	docker run -p 5000:5000 cljx/detection-service:latest

docker-compose-up:
	docker-compose up -d

docker-compose-down:
	docker-compose down

# 文档
docs:
	@echo "生成API文档..."
	@echo "文档位置: docs/"

# 性能测试
benchmark:
	python -c "from test_client import run_comprehensive_test; run_comprehensive_test()"

# 数据库迁移（如果需要）
migrate:
	@echo "暂无数据库迁移"

# 部署相关
deploy-staging:
	@echo "部署到测试环境..."
	# 添加部署脚本

deploy-prod:
	@echo "部署到生产环境..."
	# 添加部署脚本

# 监控
monitor:
	@echo "启动性能监控..."
	python -c "from utils.performance import performance_monitor; performance_monitor.start_monitoring(); import time; time.sleep(60)"

# 备份
backup:
	@echo "创建备份..."
	mkdir -p backup/$(shell date +%Y%m%d_%H%M%S)
	cp -r logs backup/$(shell date +%Y%m%d_%H%M%S)/
	cp .env backup/$(shell date +%Y%m%d_%H%M%S)/

# 环境检查
check-env:
	@echo "检查环境配置..."
	python -c "import sys; print(f'Python版本: {sys.version}')"
	python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
	python -c "import cv2; print(f'OpenCV版本: {cv2.__version__}')"
	python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"

# 初始化项目
init:
	@echo "初始化项目..."
	mkdir -p logs debug_images result_images cache
	cp .env.example .env
	@echo "请编辑 .env 文件配置环境变量"

# 更新依赖
update-deps:
	pip list --outdated
	@echo "运行 'pip install --upgrade <package>' 来更新特定包"

# 生成requirements.txt
freeze:
	pip freeze > requirements.txt

# 代码统计
stats:
	@echo "代码统计:"
	find . -name "*.py" -not -path "./venv/*" -not -path "./.venv/*" | xargs wc -l
	@echo ""
	@echo "测试覆盖率:"
	python -m pytest tests/ --cov=. --cov-report=term-missing | tail -1

# 预提交检查
pre-commit: format lint test
	@echo "预提交检查完成"

# 发布准备
release: clean test lint
	@echo "准备发布..."
	python setup.py sdist bdist_wheel

# 开发环境设置
dev-setup: install-dev init
	@echo "开发环境设置完成"
	@echo "运行 'make run-dev' 启动开发服务器"
