#!/usr/bin/env python3
"""
代码质量检查脚本
运行各种代码质量检查工具
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Dict, Any


def run_command(command: List[str], description: str) -> Dict[str, Any]:
    """
    运行命令并返回结果
    
    Args:
        command: 要执行的命令列表
        description: 命令描述
    
    Returns:
        执行结果字典
    """
    print(f"\n{'='*60}")
    print(f"运行: {description}")
    print(f"命令: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return {
            "success": result.returncode == 0,
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
    except subprocess.TimeoutExpired:
        print("命令执行超时")
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": "命令执行超时"
        }
    except Exception as e:
        print(f"执行命令时发生错误: {str(e)}")
        return {
            "success": False,
            "returncode": -1,
            "stdout": "",
            "stderr": str(e)
        }


def check_python_syntax():
    """检查Python语法"""
    python_files = []
    
    # 查找所有Python文件
    for root, dirs, files in os.walk('.'):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['.venv', 'venv', '__pycache__', '.git']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    print(f"找到 {len(python_files)} 个Python文件")
    
    syntax_errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
        except SyntaxError as e:
            syntax_errors.append(f"{file_path}: {str(e)}")
        except Exception as e:
            syntax_errors.append(f"{file_path}: {str(e)}")
    
    if syntax_errors:
        print("发现语法错误:")
        for error in syntax_errors:
            print(f"  - {error}")
        return False
    else:
        print("✓ 所有Python文件语法检查通过")
        return True


def run_flake8():
    """运行flake8代码风格检查"""
    return run_command(
        ["python", "-m", "flake8", ".", "--max-line-length=100", "--ignore=E501,W503"],
        "Flake8 代码风格检查"
    )


def run_pylint():
    """运行pylint代码质量检查"""
    return run_command(
        ["python", "-m", "pylint", "*.py", "--disable=C0114,C0115,C0116"],
        "Pylint 代码质量检查"
    )


def run_mypy():
    """运行mypy类型检查"""
    return run_command(
        ["python", "-m", "mypy", ".", "--ignore-missing-imports"],
        "MyPy 类型检查"
    )


def run_bandit():
    """运行bandit安全检查"""
    return run_command(
        ["python", "-m", "bandit", "-r", ".", "-x", "tests/,venv/,.venv/"],
        "Bandit 安全检查"
    )


def run_tests():
    """运行单元测试"""
    return run_command(
        ["python", "-m", "pytest", "tests/", "-v", "--tb=short"],
        "单元测试"
    )


def check_requirements():
    """检查依赖文件"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"✓ requirements.txt 包含 {len(lines)} 个依赖")
        
        # 检查是否有版本固定
        pinned_count = sum(1 for line in lines if '==' in line)
        print(f"✓ {pinned_count} 个依赖已固定版本")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取requirements.txt失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("CLJX Detection Service - 代码质量检查")
    print("=" * 60)
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    results = {}
    
    # 1. Python语法检查
    print("\n1. Python语法检查")
    results['syntax'] = check_python_syntax()
    
    # 2. 依赖检查
    print("\n2. 依赖文件检查")
    results['requirements'] = check_requirements()
    
    # 3. 代码风格检查（如果安装了flake8）
    try:
        import flake8
        results['flake8'] = run_flake8()
    except ImportError:
        print("\n跳过Flake8检查（未安装）")
        print("安装命令: pip install flake8")
        results['flake8'] = {"success": None, "message": "未安装"}
    
    # 4. 类型检查（如果安装了mypy）
    try:
        import mypy
        results['mypy'] = run_mypy()
    except ImportError:
        print("\n跳过MyPy检查（未安装）")
        print("安装命令: pip install mypy")
        results['mypy'] = {"success": None, "message": "未安装"}
    
    # 5. 安全检查（如果安装了bandit）
    try:
        import bandit
        results['bandit'] = run_bandit()
    except ImportError:
        print("\n跳过Bandit检查（未安装）")
        print("安装命令: pip install bandit")
        results['bandit'] = {"success": None, "message": "未安装"}
    
    # 6. 单元测试（如果安装了pytest）
    try:
        import pytest
        results['tests'] = run_tests()
    except ImportError:
        print("\n跳过单元测试（未安装pytest）")
        print("安装命令: pip install pytest")
        results['tests'] = {"success": None, "message": "未安装"}
    
    # 总结
    print("\n" + "=" * 60)
    print("代码质量检查总结")
    print("=" * 60)
    
    for check_name, result in results.items():
        if isinstance(result, bool):
            status = "✓ 通过" if result else "❌ 失败"
        elif isinstance(result, dict):
            if result.get("success") is None:
                status = "⚠️  跳过"
            elif result.get("success"):
                status = "✓ 通过"
            else:
                status = "❌ 失败"
        else:
            status = "❓ 未知"
        
        print(f"{check_name:15} : {status}")
    
    # 检查是否有失败的项目
    failed_checks = [
        name for name, result in results.items()
        if (isinstance(result, bool) and not result) or
           (isinstance(result, dict) and result.get("success") is False)
    ]
    
    if failed_checks:
        print(f"\n❌ {len(failed_checks)} 项检查失败: {', '.join(failed_checks)}")
        sys.exit(1)
    else:
        print("\n✓ 所有检查通过！")
        sys.exit(0)


if __name__ == "__main__":
    main()
