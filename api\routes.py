"""
API路由模块
定义Flask应用的所有API端点
"""

from flask import Flask, request, jsonify
from datetime import datetime
from typing import Dict, Any

from services.detection_service import RealTimeDetectionService
from utils.logger import get_logger
from config import API_CONFIG

logger = get_logger(__name__)

# 全局服务实例
detection_service = None


def create_app() -> Flask:
    """
    创建并配置Flask应用
    
    Returns:
        配置好的Flask应用实例
    """
    app = Flask(__name__)
    
    # 初始化检测服务
    global detection_service
    try:
        detection_service = RealTimeDetectionService()
        logger.info("检测服务初始化成功")
    except Exception as e:
        logger.error(f"检测服务初始化失败: {str(e)}")
        raise
    
    # 注册路由
    register_routes(app)
    
    return app


def register_routes(app: Flask) -> None:
    """
    注册所有API路由
    
    Args:
        app: Flask应用实例
    """
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        try:
            status = detection_service.get_service_status()
            return jsonify(status), 200
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return jsonify({
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }), 500
    
    @app.route('/detect', methods=['POST'])
    def detect_batch():
        """批量检测接口"""
        try:
            # 验证请求数据
            if not request.is_json:
                return create_error_response("请求必须是JSON格式"), 400
            
            data = request.get_json()
            if not data:
                return create_error_response("请求体不能为空"), 400
            
            # 验证必要参数
            if 'image_urls' not in data:
                return create_error_response("缺少必要参数 image_urls"), 400
            
            image_urls = data['image_urls']
            detection_types = data.get('detection_types', [0, 1])  # 0:手触螺丝, 1:锤击螺丝
            
            # 验证detection_types
            if not isinstance(detection_types, list):
                return create_error_response("detection_types 必须是列表类型"), 400
            
            valid_types = [0, 1]
            for dt in detection_types:
                if dt not in valid_types:
                    return create_error_response(f"无效的检测类型: {dt}，支持的类型: {valid_types}"), 400
            
            # 处理检测
            result = detection_service.process_batch(image_urls, detection_types)
            
            # 如果服务返回错误，使用相应的HTTP状态码
            status_code = 200 if result.get('code') == 200 else 400
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"检测服务异常: {str(e)}")
            return create_error_response(f"检测服务异常: {str(e)}"), 500
    
    @app.route('/detect/single', methods=['POST'])
    def detect_single():
        """单张图片检测接口"""
        try:
            if not request.is_json:
                return create_error_response("请求必须是JSON格式"), 400
            
            data = request.get_json()
            if not data:
                return create_error_response("请求体不能为空"), 400
            
            if 'image_url' not in data:
                return create_error_response("缺少必要参数 image_url"), 400
            
            image_url = data['image_url']
            detection_types = data.get('detection_types', [0, 1])
            
            # 验证URL
            if not isinstance(image_url, str) or not image_url.strip():
                return create_error_response("image_url 必须是非空字符串"), 400
            
            # 处理检测
            result = detection_service.process_single_image(image_url, detection_types)
            
            status_code = 200 if result.get('code') == 200 else 400
            return jsonify(result), status_code
            
        except Exception as e:
            logger.error(f"单图检测异常: {str(e)}")
            return create_error_response(f"单图检测异常: {str(e)}"), 500
    
    @app.route('/api/info', methods=['GET'])
    def api_info():
        """API信息接口"""
        return jsonify({
            "name": "CLJX Detection Service",
            "version": "1.0.0",
            "description": "基于YOLO11-OBB的实时目标检测和行为分析服务",
            "endpoints": {
                "/health": "健康检查",
                "/detect": "批量图片检测",
                "/detect/single": "单张图片检测",
                "/api/info": "API信息"
            },
            "detection_types": {
                "0": "手触螺丝检测",
                "1": "锤击螺丝检测"
            },
            "supported_formats": ["jpg", "jpeg", "png", "bmp"],
            "max_images_per_batch": API_CONFIG['max_image_urls'],
            "request_timeout": f"{API_CONFIG['request_timeout']}秒"
        })
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return jsonify({
            "code": 404,
            "msg": "接口不存在",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """405错误处理"""
        return jsonify({
            "code": 405,
            "msg": "请求方法不被允许",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        }), 405
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        logger.error(f"内部服务器错误: {str(error)}")
        return jsonify({
            "code": 500,
            "msg": "内部服务器错误",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
        }), 500


def create_error_response(error_msg: str) -> Dict[str, Any]:
    """
    创建标准错误响应
    
    Args:
        error_msg: 错误信息
        
    Returns:
        错误响应字典
    """
    return {
        "code": 500,
        "msg": error_msg,
        "cost_time": "0.000秒",
        "request_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "end_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
        "data": {}
    }
